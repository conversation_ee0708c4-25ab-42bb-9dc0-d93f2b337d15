import { NextRequest, NextResponse } from 'next/server';
import { ethers } from 'ethers';

const SECURITY_TOKEN_CORE_ABI = [
  "function mint(address to, uint256 amount) external",
  "function balanceOf(address account) external view returns (uint256)",
  "function totalSupply() external view returns (uint256)",
  "function maxSupply() external view returns (uint256)"
];

export async function POST(request: NextRequest) {
  try {
    const { tokenAddress, toAddress, amount } = await request.json();

    if (!tokenAddress || !toAddress || !amount) {
      return NextResponse.json(
        { error: 'Token address, recipient address, and amount are required' },
        { status: 400 }
      );
    }

    // Validate addresses
    if (!ethers.isAddress(tokenAddress) || !ethers.isAddress(toAddress)) {
      return NextResponse.json(
        { error: 'Invalid address format' },
        { status: 400 }
      );
    }

    // Validate amount
    const amountNum = parseInt(amount);
    if (isNaN(amountNum) || amountNum <= 0) {
      return NextResponse.json(
        { error: 'Invalid amount value' },
        { status: 400 }
      );
    }

    // Get environment variables
    const rpcUrl = process.env.AMOY_RPC_URL;
    const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;

    if (!rpcUrl || !privateKey) {
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      );
    }

    // Setup provider and signer
    const provider = new ethers.JsonRpcProvider(rpcUrl);
    const signer = new ethers.Wallet(privateKey, provider);

    // Get token contract
    const tokenContract = new ethers.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);

    // Get current values for comparison
    const currentBalance = await tokenContract.balanceOf(toAddress);
    const currentTotalSupply = await tokenContract.totalSupply();
    const maxSupply = await tokenContract.maxSupply();
    
    console.log('Current balance:', currentBalance.toString());
    console.log('Current total supply:', currentTotalSupply.toString());
    console.log('Max supply:', maxSupply.toString());
    console.log('Amount to mint:', amount);

    // Convert to proper units (assuming 0 decimals based on our token)
    const amountBN = ethers.parseUnits(amount, 0);

    // Check if minting would exceed max supply
    if (currentTotalSupply + amountBN > maxSupply) {
      return NextResponse.json(
        { error: 'Minting would exceed maximum supply' },
        { status: 400 }
      );
    }

    // Mint tokens
    const tx = await tokenContract.mint(toAddress, amountBN);
    await tx.wait();

    // Verify minting
    const newBalance = await tokenContract.balanceOf(toAddress);
    const newTotalSupply = await tokenContract.totalSupply();

    console.log('Tokens minted successfully:', tx.hash);

    return NextResponse.json({
      success: true,
      message: 'Tokens minted successfully',
      txHash: tx.hash,
      toAddress,
      amount: amount,
      oldBalance: currentBalance.toString(),
      newBalance: newBalance.toString(),
      oldTotalSupply: currentTotalSupply.toString(),
      newTotalSupply: newTotalSupply.toString(),
      tokenAddress
    });

  } catch (error: any) {
    console.error('Error minting tokens:', error);
    return NextResponse.json(
      { error: `Failed to mint tokens: ${error.message}` },
      { status: 500 }
    );
  }
}
