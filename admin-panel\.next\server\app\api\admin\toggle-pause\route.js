/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/toggle-pause/route";
exports.ids = ["app/api/admin/toggle-pause/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Ftoggle-pause%2Froute&page=%2Fapi%2Fadmin%2Ftoggle-pause%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Ftoggle-pause%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Ftoggle-pause%2Froute&page=%2Fapi%2Fadmin%2Ftoggle-pause%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Ftoggle-pause%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_admin_toggle_pause_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/toggle-pause/route.ts */ \"(rsc)/./src/app/api/admin/toggle-pause/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/toggle-pause/route\",\n        pathname: \"/api/admin/toggle-pause\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/toggle-pause/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\admin\\\\toggle-pause\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_admin_toggle_pause_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Ftoggle-pause%2Froute&page=%2Fapi%2Fadmin%2Ftoggle-pause%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Ftoggle-pause%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/toggle-pause/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/admin/toggle-pause/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/wallet/wallet.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/utils/units.js\");\n\n\nconst SECURITY_TOKEN_CORE_ABI = [\n    \"function pause() external\",\n    \"function unpause() external\",\n    \"function paused() external view returns (bool)\"\n];\nasync function POST(request) {\n    try {\n        const { tokenAddress, action } = await request.json();\n        if (!tokenAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token address is required'\n            }, {\n                status: 400\n            });\n        }\n        if (!action || ![\n            'pause',\n            'unpause'\n        ].includes(action)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Action must be \"pause\" or \"unpause\"'\n            }, {\n                status: 400\n            });\n        }\n        // Get environment variables\n        const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;\n        const rpcUrl = process.env.AMOY_RPC_URL || \"https://rpc-amoy.polygon.technology/\";\n        if (!privateKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Admin private key not configured'\n            }, {\n                status: 500\n            });\n        }\n        if (!rpcUrl) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'RPC URL not configured'\n            }, {\n                status: 500\n            });\n        }\n        console.log(`Attempting to ${action} token at address:`, tokenAddress);\n        // Setup provider and signer\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_1__.JsonRpcProvider(rpcUrl);\n        const signer = new ethers__WEBPACK_IMPORTED_MODULE_2__.Wallet(privateKey, provider);\n        // Get token contract\n        const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_3__.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);\n        // Check current pause status\n        const currentlyPaused = await tokenContract.paused();\n        console.log('Current pause status:', currentlyPaused);\n        // Validate action makes sense\n        if (action === 'pause' && currentlyPaused) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'Token is already paused',\n                alreadyInDesiredState: true\n            });\n        }\n        if (action === 'unpause' && !currentlyPaused) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                message: 'Token is already unpaused',\n                alreadyInDesiredState: true\n            });\n        }\n        // Multiple strategies to handle Amoy testnet issues\n        const strategies = [\n            // Strategy 1: Standard contract call\n            async ()=>{\n                const tx = action === 'pause' ? await tokenContract.pause({\n                    gasLimit: 500000,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_4__.parseUnits('30', 'gwei')\n                }) : await tokenContract.unpause({\n                    gasLimit: 500000,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_4__.parseUnits('30', 'gwei')\n                });\n                return tx.wait();\n            },\n            // Strategy 2: Raw transaction (like the working script)\n            async ()=>{\n                const functionSelector = action === 'pause' ? '0x8456cb59' : '0x3f4ba83a';\n                const nonce = await signer.getNonce();\n                const tx = await signer.sendTransaction({\n                    to: tokenAddress,\n                    data: functionSelector,\n                    gasLimit: 500000,\n                    gasPrice: ethers__WEBPACK_IMPORTED_MODULE_4__.parseUnits('30', 'gwei'),\n                    nonce: nonce\n                });\n                return tx.wait();\n            }\n        ];\n        let lastError;\n        for(let i = 0; i < strategies.length; i++){\n            try {\n                console.log(`Attempting ${action} strategy ${i + 1}...`);\n                const receipt = await strategies[i]();\n                console.log(`${action} successful with strategy ${i + 1}. Tx hash:`, receipt.hash);\n                // Verify the action worked\n                const newPauseStatus = await tokenContract.paused();\n                const expectedStatus = action === 'pause';\n                if (newPauseStatus === expectedStatus) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        success: true,\n                        message: `Token ${action}d successfully`,\n                        transactionHash: receipt.hash,\n                        blockNumber: receipt.blockNumber,\n                        strategy: i + 1\n                    });\n                } else {\n                    throw new Error(`Transaction succeeded but pause status didn't change as expected`);\n                }\n            } catch (error) {\n                console.error(`Strategy ${i + 1} failed:`, error);\n                lastError = error;\n                // If this is not the last strategy, wait and try the next one\n                if (i < strategies.length - 1) {\n                    await new Promise((resolve)=>setTimeout(resolve, 2000));\n                }\n            }\n        }\n        // All strategies failed\n        console.error('All strategies failed. Last error:', lastError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Failed to ${action} token after trying multiple methods`,\n            details: lastError?.message || 'Unknown error',\n            suggestion: 'The Amoy testnet may be experiencing issues. Please try again in a few minutes.'\n        }, {\n            status: 500\n        });\n    } catch (error) {\n        console.error('Toggle pause API error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/toggle-pause/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/aes-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Ftoggle-pause%2Froute&page=%2Fapi%2Fadmin%2Ftoggle-pause%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Ftoggle-pause%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();