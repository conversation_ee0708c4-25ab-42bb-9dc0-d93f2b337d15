[{"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "activator", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "expiry", "type": "uint256"}], "name": "EmergencyModeActivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "deactivator", "type": "address"}], "name": "EmergencyModeDeactivated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "moduleId", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "proxy", "type": "address"}, {"indexed": false, "internalType": "address", "name": "oldImplementation", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newImplementation", "type": "address"}], "name": "EmergencyUpgradeExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "moduleId", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "proxy", "type": "address"}], "name": "ModuleRegistered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "moduleId", "type": "bytes32"}], "name": "ModuleUnregistered", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "upgradeId", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "moduleId", "type": "bytes32"}], "name": "UpgradeCancelled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "upgradeId", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "moduleId", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "proxy", "type": "address"}, {"indexed": false, "internalType": "address", "name": "oldImplementation", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newImplementation", "type": "address"}, {"indexed": false, "internalType": "string", "name": "version", "type": "string"}], "name": "UpgradeExecuted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "upgradeId", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "moduleId", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "proxy", "type": "address"}, {"indexed": false, "internalType": "address", "name": "newImplementation", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "executeTime", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "description", "type": "string"}], "name": "UpgradeScheduled", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "EMERGENCY_MODE_DURATION", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "EMERGENCY_UPGRADE_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "UPGRADE_DELAY", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "UPGRADE_MANAGER_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "activateEmergencyMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "upgradeId", "type": "bytes32"}], "name": "cancelUpgrade", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "deactivateEmergencyMode", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "emergencyMode", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "moduleId", "type": "bytes32"}, {"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "string", "name": "description", "type": "string"}], "name": "emergencyUpgrade", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "upgradeId", "type": "bytes32"}], "name": "executeUpgrade", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getPendingUpgradeIds", "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getRegisteredModules", "outputs": [{"internalType": "bytes32[]", "name": "", "type": "bytes32[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "moduleId", "type": "bytes32"}], "name": "getUpgradeHistory", "outputs": [{"components": [{"internalType": "address", "name": "oldImplementation", "type": "address"}, {"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "address", "name": "executor", "type": "address"}, {"internalType": "string", "name": "version", "type": "string"}, {"internalType": "bool", "name": "isEmergency", "type": "bool"}, {"internalType": "string", "name": "description", "type": "string"}], "internalType": "struct UpgradeManager.UpgradeRecord[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "admin", "type": "address"}], "name": "initialize", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "isEmergencyModeActive", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "moduleProxies", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "name": "pendingUpgrades", "outputs": [{"internalType": "bytes32", "name": "moduleId", "type": "bytes32"}, {"internalType": "address", "name": "proxy", "type": "address"}, {"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "uint256", "name": "executeTime", "type": "uint256"}, {"internalType": "bool", "name": "executed", "type": "bool"}, {"internalType": "bool", "name": "cancelled", "type": "bool"}, {"internalType": "string", "name": "description", "type": "string"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "moduleId", "type": "bytes32"}, {"internalType": "address", "name": "proxy", "type": "address"}], "name": "registerModule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "moduleId", "type": "bytes32"}, {"internalType": "address", "name": "newImplementation", "type": "address"}, {"internalType": "string", "name": "description", "type": "string"}], "name": "scheduleUpgrade", "outputs": [{"internalType": "bytes32", "name": "upgradeId", "type": "bytes32"}], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "moduleId", "type": "bytes32"}], "name": "unregisterModule", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]