/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/tokens/route";
exports.ids = ["app/api/tokens/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_tokens_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/tokens/route.ts */ \"(rsc)/./src/app/api/tokens/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/tokens/route\",\n        pathname: \"/api/tokens\",\n        filename: \"route\",\n        bundlePath: \"app/api/tokens/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\tokens\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_tokens_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/tokens/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/tokens/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/constants/addresses.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var _lib_prisma__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../lib/prisma */ \"(rsc)/./src/lib/prisma.ts\");\n/* harmony import */ var _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../contracts/SecurityTokenFactory.json */ \"(rsc)/./src/contracts/SecurityTokenFactory.json\");\n/* harmony import */ var _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../contracts/SecurityToken.json */ \"(rsc)/./src/contracts/SecurityToken.json\");\n/* harmony import */ var _contracts_ModularTokenFactory_json__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../contracts/ModularTokenFactory.json */ \"(rsc)/./src/contracts/ModularTokenFactory.json\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../../contracts/SecurityTokenCore.json */ \"(rsc)/./src/contracts/SecurityTokenCore.json\");\n\n\n\n\n\n\n\n// RPC URLs for different networks\nconst RPC_URLS = {\n    amoy: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/',\n    polygon: process.env.POLYGON_RPC_URL || 'https://polygon-rpc.com',\n    unknown: process.env.AMOY_RPC_URL || 'https://rpc-amoy.polygon.technology/'\n};\n// Factory contract addresses\nconst FACTORY_ADDRESS = process.env.FACTORY_ADDRESS || '******************************************';\nconst MODULAR_FACTORY_ADDRESS = process.env.AMOY_MODULAR_TOKEN_FACTORY_ADDRESS || '******************************************';\n// Helper function to determine if an address looks like a real contract address\nfunction isRealContractAddress(address) {\n    // Skip obvious test addresses\n    if (!address || address === ethers__WEBPACK_IMPORTED_MODULE_6__.ZeroAddress) {\n        return false;\n    }\n    // Skip addresses that are clearly test patterns (all zeros with small numbers)\n    const testPatterns = [\n        /^0x0+[0-9a-f]{1,2}$/i,\n        /^******************************************$/i,\n        /^0x0+$/i // All zeros\n    ];\n    return !testPatterns.some((pattern)=>pattern.test(address));\n}\n// Helper function to fetch modular tokens from the ModularTokenFactory\nasync function fetchModularTokens(provider) {\n    try {\n        console.log(`Fetching modular tokens from factory: ${MODULAR_FACTORY_ADDRESS}`);\n        const modularFactory = new ethers__WEBPACK_IMPORTED_MODULE_7__.Contract(MODULAR_FACTORY_ADDRESS, _contracts_ModularTokenFactory_json__WEBPACK_IMPORTED_MODULE_4__, provider);\n        // Get token count\n        const tokenCount = await modularFactory.getDeployedTokensCount();\n        console.log(`Modular factory reports ${tokenCount} deployed tokens`);\n        if (tokenCount === 0) {\n            return [];\n        }\n        // Get all active tokens\n        const activeTokens = await modularFactory.getActiveTokens();\n        console.log(\"Retrieved modular token addresses:\", activeTokens);\n        const tokens = [];\n        for (const address of activeTokens){\n            try {\n                // Get token info from factory\n                const tokenInfo = await modularFactory.getTokenInfo(address);\n                // Get additional details from the token contract\n                const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_7__.Contract(address, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_5__, provider);\n                const [totalSupply, version] = await Promise.all([\n                    tokenContract.totalSupply(),\n                    tokenContract.version().catch(()=>\"1.0.0\")\n                ]);\n                // Get metadata if available\n                let metadata = null;\n                try {\n                    metadata = await tokenContract.getMetadata();\n                } catch (e) {\n                // Metadata might not be available\n                }\n                tokens.push({\n                    id: `modular-${address}`,\n                    address,\n                    name: tokenInfo.name,\n                    symbol: tokenInfo.symbol,\n                    decimals: Number(tokenInfo.decimals),\n                    maxSupply: ethers__WEBPACK_IMPORTED_MODULE_8__.formatUnits(tokenInfo.maxSupply, tokenInfo.decimals),\n                    totalSupply: ethers__WEBPACK_IMPORTED_MODULE_8__.formatUnits(totalSupply, tokenInfo.decimals),\n                    tokenType: 'Modular Security Token',\n                    tokenPrice: metadata?.tokenPrice || 'N/A',\n                    currency: 'USD',\n                    network: 'amoy',\n                    hasKYC: true,\n                    isActive: tokenInfo.isActive,\n                    adminAddress: tokenInfo.admin,\n                    whitelistAddress: null,\n                    transactionHash: null,\n                    blockNumber: null,\n                    createdAt: new Date(Number(tokenInfo.deploymentTime) * 1000).toISOString(),\n                    updatedAt: new Date().toISOString(),\n                    version: version,\n                    isModular: true\n                });\n            } catch (error) {\n                console.warn(`Failed to load modular token details for ${address}:`, error);\n            }\n        }\n        console.log(`Successfully loaded ${tokens.length} modular tokens`);\n        return tokens;\n    } catch (error) {\n        console.error('Error fetching modular tokens:', error);\n        return [];\n    }\n}\n// GET /api/tokens - Get all tokens from database and optionally sync with blockchain\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const source = searchParams.get('source') || 'database'; // 'database', 'blockchain', or 'both'\n        if (source === 'database' || source === 'both') {\n            // Get tokens from database\n            const dbTokens = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.findMany({\n                orderBy: {\n                    createdAt: 'desc'\n                },\n                include: {\n                    transactions: {\n                        take: 5,\n                        orderBy: {\n                            createdAt: 'desc'\n                        }\n                    }\n                }\n            });\n            if (source === 'database') {\n                // Sync totalSupply from blockchain for database tokens if needed\n                const tokensWithUpdatedSupply = await Promise.all(dbTokens.map(async (token)=>{\n                    // Only attempt blockchain sync for tokens that:\n                    // 1. Have totalSupply of 0 or not set\n                    // 2. Have a real-looking address (not test addresses)\n                    // 3. Are on a supported network\n                    const shouldSyncFromBlockchain = (token.totalSupply === '0' || !token.totalSupply) && isRealContractAddress(token.address) && (token.network === 'amoy' || token.network === 'polygon');\n                    if (shouldSyncFromBlockchain) {\n                        try {\n                            const network = token.network || 'amoy';\n                            const rpcUrl = RPC_URLS[network] || RPC_URLS.amoy;\n                            const provider = new ethers__WEBPACK_IMPORTED_MODULE_9__.JsonRpcProvider(rpcUrl);\n                            // First check if there's code at this address\n                            const code = await provider.getCode(token.address);\n                            if (code === '0x') {\n                                console.log(`Skipping blockchain sync for ${token.address} - no contract deployed`);\n                                return token;\n                            }\n                            const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_7__.Contract(token.address, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__.abi, provider);\n                            const totalSupplyRaw = await tokenContract.totalSupply();\n                            const decimals = token.decimals || 18;\n                            const totalSupply = decimals === 0 ? totalSupplyRaw.toString() : ethers__WEBPACK_IMPORTED_MODULE_8__.formatUnits(totalSupplyRaw, decimals);\n                            console.log(`Successfully synced totalSupply for ${token.symbol}: ${totalSupply}`);\n                            // Update the database with the fetched totalSupply\n                            await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.update({\n                                where: {\n                                    id: token.id\n                                },\n                                data: {\n                                    totalSupply\n                                }\n                            });\n                            return {\n                                ...token,\n                                totalSupply\n                            };\n                        } catch (error) {\n                            console.warn(`Could not fetch totalSupply for token ${token.address}: ${error.message}`);\n                            return token;\n                        }\n                    }\n                    return token;\n                }));\n                // Also fetch modular tokens from blockchain\n                try {\n                    const network = 'amoy';\n                    const rpcUrl = RPC_URLS[network];\n                    const provider = new ethers__WEBPACK_IMPORTED_MODULE_9__.JsonRpcProvider(rpcUrl);\n                    const modularTokens = await fetchModularTokens(provider);\n                    // Combine database tokens with modular tokens\n                    const allTokens = [\n                        ...tokensWithUpdatedSupply,\n                        ...modularTokens\n                    ];\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(allTokens);\n                } catch (error) {\n                    console.warn('Could not fetch modular tokens:', error);\n                    // Return just database tokens if modular fetch fails\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(tokensWithUpdatedSupply);\n                }\n            }\n            // If 'both', we'll merge with blockchain data below\n            if (source === 'both') {\n                // For now, just return database tokens\n                // TODO: Implement blockchain sync if needed\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(dbTokens);\n            }\n        }\n        // Blockchain-only fetch (legacy behavior)\n        const network = 'amoy'; // Default to amoy network\n        const rpcUrl = RPC_URLS[network];\n        // Create provider\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_9__.JsonRpcProvider(rpcUrl);\n        // Create factory contract instance\n        const factory = new ethers__WEBPACK_IMPORTED_MODULE_7__.Contract(FACTORY_ADDRESS, _contracts_SecurityTokenFactory_json__WEBPACK_IMPORTED_MODULE_2__.abi, provider);\n        console.log(`Fetching tokens from factory: ${FACTORY_ADDRESS}`);\n        // Get token count\n        const tokenCount = await factory.getTokenCount();\n        console.log(`Factory reports ${tokenCount} deployed tokens`);\n        if (tokenCount === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json([]);\n        }\n        // Get all token addresses\n        const tokenAddresses = await factory.getAllDeployedTokens();\n        console.log(\"Retrieved token addresses:\", tokenAddresses);\n        // Load details for each token\n        const tokens = [];\n        for (const address of tokenAddresses){\n            try {\n                const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_7__.Contract(address, _contracts_SecurityToken_json__WEBPACK_IMPORTED_MODULE_3__.abi, provider);\n                // Get basic token info\n                const [name, symbol, totalSupply, decimals, owner] = await Promise.all([\n                    tokenContract.name(),\n                    tokenContract.symbol(),\n                    tokenContract.totalSupply(),\n                    tokenContract.decimals(),\n                    tokenContract.owner()\n                ]);\n                // Get additional token details\n                let tokenType = 'UNKNOWN';\n                let securityType = 'UNKNOWN';\n                let createdAt = new Date().toISOString();\n                try {\n                    // Try to get token metadata if available\n                    const tokenDetails = await tokenContract.tokenDetails();\n                    if (tokenDetails) {\n                        // Parse token details if it's a JSON string\n                        try {\n                            const parsed = JSON.parse(tokenDetails);\n                            tokenType = parsed.tokenType || 'UNKNOWN';\n                            securityType = parsed.securityType || 'UNKNOWN';\n                        } catch  {\n                            // If not JSON, use as is\n                            tokenType = 'EQUITY'; // Default\n                            securityType = 'REGULATION_D'; // Default\n                        }\n                    }\n                } catch (error) {\n                    console.warn(`Could not fetch token details for ${address}:`, error);\n                }\n                // Try to get creation timestamp from events\n                try {\n                    const filter = factory.filters.TokenDeployed(null, address);\n                    const events = await factory.queryFilter(filter, 0, 'latest');\n                    if (events.length > 0) {\n                        const block = await provider.getBlock(events[0].blockNumber);\n                        if (block) {\n                            createdAt = new Date(block.timestamp * 1000).toISOString();\n                        }\n                    }\n                } catch (error) {\n                    console.warn(`Could not fetch creation time for ${address}:`, error);\n                }\n                tokens.push({\n                    address,\n                    name,\n                    symbol,\n                    decimals: Number(decimals),\n                    totalSupply: totalSupply.toString(),\n                    owner,\n                    securityType,\n                    tokenType,\n                    createdAt\n                });\n            } catch (error) {\n                console.warn(`Failed to load token details for ${address}:`, error);\n                // Add minimal token info even if details fail\n                tokens.push({\n                    address,\n                    name: 'Unknown Token',\n                    symbol: 'UNKNOWN',\n                    decimals: 0,\n                    totalSupply: '0',\n                    owner: ethers__WEBPACK_IMPORTED_MODULE_6__.ZeroAddress,\n                    securityType: 'UNKNOWN',\n                    tokenType: 'UNKNOWN',\n                    createdAt: new Date().toISOString()\n                });\n            }\n        }\n        console.log(`Successfully loaded ${tokens.length} tokens`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(tokens);\n    } catch (error) {\n        console.error('Error fetching tokens:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to fetch tokens from factory'\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/tokens - Create a new token record in database\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        // Validate required fields\n        const requiredFields = [\n            'address',\n            'name',\n            'symbol'\n        ];\n        for (const field of requiredFields){\n            if (!body[field]) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: `Missing required field: ${field}`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Check if token already exists\n        const existingToken = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.findUnique({\n            where: {\n                address: body.address\n            }\n        });\n        if (existingToken) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token with this address already exists'\n            }, {\n                status: 409\n            });\n        }\n        // Create new token record\n        const newToken = await _lib_prisma__WEBPACK_IMPORTED_MODULE_1__.prisma.token.create({\n            data: {\n                address: body.address,\n                transactionHash: body.transactionHash || null,\n                blockNumber: body.blockNumber || null,\n                network: body.network || 'amoy',\n                name: body.name,\n                symbol: body.symbol,\n                decimals: body.decimals !== undefined ? parseInt(body.decimals) : 18,\n                maxSupply: body.maxSupply || '1000000',\n                totalSupply: body.totalSupply || '0',\n                tokenType: body.tokenType || 'equity',\n                tokenPrice: body.tokenPrice || '10 USD',\n                currency: body.currency || 'USD',\n                bonusTiers: body.bonusTiers || null,\n                tokenImageUrl: body.tokenImageUrl || null,\n                whitelistAddress: body.whitelistAddress || null,\n                adminAddress: body.adminAddress || null,\n                hasKYC: body.hasKYC || false,\n                isActive: body.isActive !== undefined ? body.isActive : true,\n                selectedClaims: Array.isArray(body.selectedClaims) ? body.selectedClaims.join(',') : body.selectedClaims || null,\n                deployedBy: body.deployedBy || null,\n                deploymentNotes: body.deploymentNotes || null\n            }\n        });\n        console.log(`Created new token record: ${newToken.symbol} (${newToken.address})`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(newToken, {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating token:', error);\n        // Handle Prisma unique constraint errors\n        if (error.code === 'P2002') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token with this address or symbol already exists'\n            }, {\n                status: 409\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Failed to create token record'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/tokens/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/contracts/ModularTokenFactory.json":
/*!************************************************!*\
  !*** ./src/contracts/ModularTokenFactory.json ***!
  \************************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('[{"inputs":[{"internalType":"address","name":"_securityTokenImplementation","type":"address"},{"internalType":"address","name":"_upgradeManager","type":"address"},{"internalType":"address","name":"_admin","type":"address"}],"stateMutability":"nonpayable","type":"constructor"},{"inputs":[],"name":"AccessControlBadConfirmation","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bytes32","name":"neededRole","type":"bytes32"}],"name":"AccessControlUnauthorizedAccount","type":"error"},{"inputs":[],"name":"ReentrancyGuardReentrantCall","type":"error"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"previousAdminRole","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"newAdminRole","type":"bytes32"}],"name":"RoleAdminChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleGranted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"tokenAddress","type":"address"},{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"TokenDeactivated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"tokenAddress","type":"address"},{"indexed":true,"internalType":"address","name":"deployer","type":"address"},{"indexed":true,"internalType":"address","name":"admin","type":"address"},{"indexed":false,"internalType":"string","name":"name","type":"string"},{"indexed":false,"internalType":"string","name":"symbol","type":"string"},{"indexed":false,"internalType":"uint8","name":"decimals","type":"uint8"},{"indexed":false,"internalType":"uint256","name":"maxSupply","type":"uint256"}],"name":"TokenDeployed","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"oldManager","type":"address"},{"indexed":true,"internalType":"address","name":"newManager","type":"address"}],"name":"UpgradeManagerUpdated","type":"event"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"DEPLOYER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"string","name":"name_","type":"string"},{"internalType":"string","name":"symbol_","type":"string"},{"internalType":"uint8","name":"decimals_","type":"uint8"},{"internalType":"uint256","name":"maxSupply_","type":"uint256"},{"internalType":"address","name":"admin_","type":"address"},{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"},{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"deployToken","outputs":[{"internalType":"address","name":"tokenAddress","type":"address"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"","type":"uint256"}],"name":"deployedTokens","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getActiveTokens","outputs":[{"internalType":"address[]","name":"activeTokens","type":"address[]"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getDeployedTokensCount","outputs":[{"internalType":"uint256","name":"count","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"tokenAddress","type":"address"}],"name":"getTokenInfo","outputs":[{"components":[{"internalType":"string","name":"name","type":"string"},{"internalType":"string","name":"symbol","type":"string"},{"internalType":"uint8","name":"decimals","type":"uint8"},{"internalType":"uint256","name":"maxSupply","type":"uint256"},{"internalType":"address","name":"admin","type":"address"},{"internalType":"address","name":"deployer","type":"address"},{"internalType":"uint256","name":"deploymentTime","type":"uint256"},{"internalType":"bool","name":"isActive","type":"bool"}],"internalType":"struct ModularTokenFactory.TokenInfo","name":"info","type":"tuple"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"hasRole","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"tokenAddress","type":"address"}],"name":"isDeployedToken","outputs":[{"internalType":"bool","name":"isDeployed","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"securityTokenImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"upgradeManager","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"}]');

/***/ }),

/***/ "(rsc)/./src/contracts/SecurityToken.json":
/*!******************************************!*\
  !*** ./src/contracts/SecurityToken.json ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"abi":[{"inputs":[],"stateMutability":"nonpayable","type":"constructor"},{"inputs":[],"name":"AccessControlBadConfirmation","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bytes32","name":"neededRole","type":"bytes32"}],"name":"AccessControlUnauthorizedAccount","type":"error"},{"inputs":[{"internalType":"address","name":"target","type":"address"}],"name":"AddressEmptyCode","type":"error"},{"inputs":[{"internalType":"address","name":"implementation","type":"address"}],"name":"ERC1967InvalidImplementation","type":"error"},{"inputs":[],"name":"ERC1967NonPayable","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientAllowance","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientBalance","type":"error"},{"inputs":[{"internalType":"address","name":"approver","type":"address"}],"name":"ERC20InvalidApprover","type":"error"},{"inputs":[{"internalType":"address","name":"receiver","type":"address"}],"name":"ERC20InvalidReceiver","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"}],"name":"ERC20InvalidSender","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"}],"name":"ERC20InvalidSpender","type":"error"},{"inputs":[],"name":"EnforcedPause","type":"error"},{"inputs":[],"name":"ExpectedPause","type":"error"},{"inputs":[],"name":"FailedCall","type":"error"},{"inputs":[],"name":"InvalidInitialization","type":"error"},{"inputs":[],"name":"NotInitializing","type":"error"},{"inputs":[],"name":"ReentrancyGuardReentrantCall","type":"error"},{"inputs":[],"name":"UUPSUnauthorizedCallContext","type":"error"},{"inputs":[{"internalType":"bytes32","name":"slot","type":"bytes32"}],"name":"UUPSUnsupportedProxiableUUID","type":"error"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"agent","type":"address"}],"name":"AgentAdded","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"agent","type":"address"}],"name":"AgentRemoved","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":false,"internalType":"uint256","name":"timestamp","type":"uint256"}],"name":"AgreementAccepted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"spender","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Approval","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"oldCompliance","type":"address"},{"indexed":true,"internalType":"address","name":"newCompliance","type":"address"}],"name":"ComplianceUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"}],"name":"ConditionalTransfersUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"EmergencyPaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"EmergencyUnpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"ForcedTransfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes4","name":"functionSelector","type":"bytes4"},{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"FunctionPaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes4","name":"functionSelector","type":"bytes4"},{"indexed":true,"internalType":"address","name":"admin","type":"address"}],"name":"FunctionUnpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"oldRegistry","type":"address"},{"indexed":true,"internalType":"address","name":"newRegistry","type":"address"}],"name":"IdentityRegistryUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint64","name":"version","type":"uint64"}],"name":"Initialized","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"oldMaxSupply","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"newMaxSupply","type":"uint256"}],"name":"MaxSupplyUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Paused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"previousAdminRole","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"newAdminRole","type":"bytes32"}],"name":"RoleAdminChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleGranted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"string","name":"tokenImageUrl","type":"string"}],"name":"TokenImageUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"string","name":"tokenPrice","type":"string"},{"indexed":false,"internalType":"string","name":"bonusTiers","type":"string"},{"indexed":false,"internalType":"string","name":"tokenDetails","type":"string"}],"name":"TokenMetadataUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Transfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"transferId","type":"bytes32"},{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"amount","type":"uint256"}],"name":"TransferApproved","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"transferAmount","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"feeAmount","type":"uint256"}],"name":"TransferFeeCollected","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"},{"indexed":false,"internalType":"uint256","name":"feePercentage","type":"uint256"},{"indexed":false,"internalType":"address","name":"feeCollector","type":"address"}],"name":"TransferFeesUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":false,"internalType":"bool","name":"whitelisted","type":"bool"}],"name":"TransferWhitelistAddressUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"bool","name":"enabled","type":"bool"}],"name":"TransferWhitelistUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Unpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"implementation","type":"address"}],"name":"Upgraded","type":"event"},{"inputs":[],"name":"AGENT_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"TRANSFER_MANAGER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"UPGRADE_INTERFACE_VERSION","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"acceptAgreement","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"agent","type":"address"}],"name":"addAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"addToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"address","name":"spender","type":"address"}],"name":"allowance","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"approve","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"nonce","type":"uint256"}],"name":"approveTransfer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"balanceOf","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address[]","name":"accounts","type":"address[]"}],"name":"batchAddToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"bonusTiers","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"value","type":"uint256"}],"name":"burn","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"burnFrom","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"canTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"compliance","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"conditionalTransfersEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"decimals","outputs":[{"internalType":"uint8","name":"","type":"uint8"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"emergencyPause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"emergencyUnpause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"},{"internalType":"uint256","name":"nonce","type":"uint256"}],"name":"executeApprovedTransfer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"forcedTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"freezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getAgentAt","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAgentCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"getAgreementAcceptanceTimestamp","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAllAgents","outputs":[{"internalType":"address[]","name":"","type":"address[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"}],"name":"getRoleAdmin","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getTransferFeeConfig","outputs":[{"internalType":"uint256","name":"feePercentage","type":"uint256"},{"internalType":"address","name":"feeCollector","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"getTransferNonce","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"grantRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"hasAcceptedAgreement","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"hasRole","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"identityRegistry","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"string","name":"name_","type":"string"},{"internalType":"string","name":"symbol_","type":"string"},{"internalType":"uint8","name":"decimals_","type":"uint8"},{"internalType":"uint256","name":"maxSupply_","type":"uint256"},{"internalType":"address","name":"identityRegistry_","type":"address"},{"internalType":"address","name":"compliance_","type":"address"},{"internalType":"address","name":"admin_","type":"address"},{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"},{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"initialize","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"investorCountry","outputs":[{"internalType":"uint16","name":"","type":"uint16"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isAgent","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"isEmergencyPaused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes4","name":"functionSelector","type":"bytes4"}],"name":"isFunctionPaused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isTransferWhitelisted","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isVerified","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isWhitelisted","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"maxSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"mint","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"name","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"pause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"functionSelector","type":"bytes4"}],"name":"pauseFunction","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"paused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"proxiableUUID","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"agent","type":"address"}],"name":"removeAgent","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"removeFromWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"callerConfirmation","type":"address"}],"name":"renounceRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"revokeRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"}],"name":"setConditionalTransfers","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"},{"internalType":"uint256","name":"feePercentage","type":"uint256"},{"internalType":"address","name":"feeCollector","type":"address"}],"name":"setTransferFees","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bool","name":"enabled","type":"bool"}],"name":"setTransferWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bool","name":"whitelisted","type":"bool"}],"name":"setTransferWhitelistAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"interfaceId","type":"bytes4"}],"name":"supportsInterface","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"symbol","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenDetails","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenImageUrl","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"tokenPrice","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"totalSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"transferFeesEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transferFrom","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"transferWhitelistEnabled","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"unfreezeAddress","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"unpause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes4","name":"functionSelector","type":"bytes4"}],"name":"unpauseFunction","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newCompliance","type":"address"}],"name":"updateCompliance","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newIdentityRegistry","type":"address"}],"name":"updateIdentityRegistry","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"newMaxSupply","type":"uint256"}],"name":"updateMaxSupply","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"updateTokenImageUrl","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"}],"name":"updateTokenMetadata","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"newImplementation","type":"address"},{"internalType":"bytes","name":"data","type":"bytes"}],"name":"upgradeToAndCall","outputs":[],"stateMutability":"payable","type":"function"},{"inputs":[],"name":"version","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"pure","type":"function"}]}');

/***/ }),

/***/ "(rsc)/./src/contracts/SecurityTokenCore.json":
/*!**********************************************!*\
  !*** ./src/contracts/SecurityTokenCore.json ***!
  \**********************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('[{"inputs":[],"name":"AccessControlBadConfirmation","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bytes32","name":"neededRole","type":"bytes32"}],"name":"AccessControlUnauthorizedAccount","type":"error"},{"inputs":[{"internalType":"address","name":"target","type":"address"}],"name":"AddressEmptyCode","type":"error"},{"inputs":[{"internalType":"address","name":"implementation","type":"address"}],"name":"ERC1967InvalidImplementation","type":"error"},{"inputs":[],"name":"ERC1967NonPayable","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"allowance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientAllowance","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"},{"internalType":"uint256","name":"balance","type":"uint256"},{"internalType":"uint256","name":"needed","type":"uint256"}],"name":"ERC20InsufficientBalance","type":"error"},{"inputs":[{"internalType":"address","name":"approver","type":"address"}],"name":"ERC20InvalidApprover","type":"error"},{"inputs":[{"internalType":"address","name":"receiver","type":"address"}],"name":"ERC20InvalidReceiver","type":"error"},{"inputs":[{"internalType":"address","name":"sender","type":"address"}],"name":"ERC20InvalidSender","type":"error"},{"inputs":[{"internalType":"address","name":"spender","type":"address"}],"name":"ERC20InvalidSpender","type":"error"},{"inputs":[],"name":"EnforcedPause","type":"error"},{"inputs":[],"name":"ExpectedPause","type":"error"},{"inputs":[],"name":"FailedCall","type":"error"},{"inputs":[],"name":"InvalidInitialization","type":"error"},{"inputs":[],"name":"NotInitializing","type":"error"},{"inputs":[],"name":"ReentrancyGuardReentrantCall","type":"error"},{"inputs":[],"name":"UUPSUnauthorizedCallContext","type":"error"},{"inputs":[{"internalType":"bytes32","name":"slot","type":"bytes32"}],"name":"UUPSUnsupportedProxiableUUID","type":"error"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"owner","type":"address"},{"indexed":true,"internalType":"address","name":"spender","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Approval","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"ForcedTransfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint64","name":"version","type":"uint64"}],"name":"Initialized","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"uint256","name":"oldMaxSupply","type":"uint256"},{"indexed":false,"internalType":"uint256","name":"newMaxSupply","type":"uint256"}],"name":"MaxSupplyUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"moduleId","type":"bytes32"},{"indexed":true,"internalType":"address","name":"moduleAddress","type":"address"}],"name":"ModuleRegistered","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"moduleId","type":"bytes32"}],"name":"ModuleUnregistered","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Paused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"previousAdminRole","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"newAdminRole","type":"bytes32"}],"name":"RoleAdminChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleGranted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"string","name":"tokenPrice","type":"string"},{"indexed":false,"internalType":"string","name":"bonusTiers","type":"string"},{"indexed":false,"internalType":"string","name":"tokenDetails","type":"string"}],"name":"TokenMetadataUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"from","type":"address"},{"indexed":true,"internalType":"address","name":"to","type":"address"},{"indexed":false,"internalType":"uint256","name":"value","type":"uint256"}],"name":"Transfer","type":"event"},{"anonymous":false,"inputs":[{"indexed":false,"internalType":"address","name":"account","type":"address"}],"name":"Unpaused","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"implementation","type":"address"}],"name":"Upgraded","type":"event"},{"inputs":[],"name":"AGENT_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"MODULE_MANAGER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"TRANSFER_MANAGER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"addToWhitelist","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"owner","type":"address"},{"internalType":"address","name":"spender","type":"address"}],"name":"allowance","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"spender","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"approve","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"balanceOf","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"canTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"decimals","outputs":[{"internalType":"uint8","name":"","type":"uint8"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"from","type":"address"},{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"forcedTransfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"getTokenMetadata","outputs":[{"internalType":"string","name":"tokenPrice","type":"string"},{"internalType":"string","name":"bonusTiers","type":"string"},{"internalType":"string","name":"tokenDetails","type":"string"},{"internalType":"string","name":"tokenImageUrl","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"string","name":"name_","type":"string"},{"internalType":"string","name":"symbol_","type":"string"},{"internalType":"uint8","name":"decimals_","type":"uint8"},{"internalType":"uint256","name":"maxSupply_","type":"uint256"},{"internalType":"address","name":"admin_","type":"address"},{"internalType":"string","name":"tokenPrice_","type":"string"},{"internalType":"string","name":"bonusTiers_","type":"string"},{"internalType":"string","name":"tokenDetails_","type":"string"},{"internalType":"string","name":"tokenImageUrl_","type":"string"}],"name":"initialize","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"address","name":"account","type":"address"}],"name":"isWhitelisted","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"maxSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"amount","type":"uint256"}],"name":"mint","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"name","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"pause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"paused","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"symbol","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"totalSupply","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"to","type":"address"},{"internalType":"uint256","name":"value","type":"uint256"}],"name":"transfer","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"unpause","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"version","outputs":[{"internalType":"string","name":"","type":"string"}],"stateMutability":"pure","type":"function"}]');

/***/ }),

/***/ "(rsc)/./src/contracts/SecurityTokenFactory.json":
/*!*************************************************!*\
  !*** ./src/contracts/SecurityTokenFactory.json ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = /*#__PURE__*/JSON.parse('{"abi":[{"inputs":[{"internalType":"address","name":"admin","type":"address"}],"stateMutability":"nonpayable","type":"constructor"},{"inputs":[],"name":"AccessControlBadConfirmation","type":"error"},{"inputs":[{"internalType":"address","name":"account","type":"address"},{"internalType":"bytes32","name":"neededRole","type":"bytes32"}],"name":"AccessControlUnauthorizedAccount","type":"error"},{"inputs":[],"name":"ReentrancyGuardReentrantCall","type":"error"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"oldTokenImplementation","type":"address"},{"indexed":true,"internalType":"address","name":"newTokenImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"oldWhitelistImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"newWhitelistImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"oldWhitelistWithKYCImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"newWhitelistWithKYCImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"oldComplianceImplementation","type":"address"},{"indexed":false,"internalType":"address","name":"newComplianceImplementation","type":"address"}],"name":"ImplementationsUpdated","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"previousAdminRole","type":"bytes32"},{"indexed":true,"internalType":"bytes32","name":"newAdminRole","type":"bytes32"}],"name":"RoleAdminChanged","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleGranted","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"bytes32","name":"role","type":"bytes32"},{"indexed":true,"internalType":"address","name":"account","type":"address"},{"indexed":true,"internalType":"address","name":"sender","type":"address"}],"name":"RoleRevoked","type":"event"},{"anonymous":false,"inputs":[{"indexed":true,"internalType":"address","name":"tokenAddress","type":"address"},{"indexed":true,"internalType":"address","name":"identityRegistryAddress","type":"address"},{"indexed":true,"internalType":"address","name":"complianceAddress","type":"address"},{"indexed":false,"internalType":"string","name":"name","type":"string"},{"indexed":false,"internalType":"string","name":"symbol","type":"string"},{"indexed":false,"internalType":"uint8","name":"decimals","type":"uint8"},{"indexed":false,"internalType":"uint256","name":"maxSupply","type":"uint256"},{"indexed":false,"internalType":"address","name":"admin","type":"address"},{"indexed":false,"internalType":"bool","name":"hasKYC","type":"bool"},{"indexed":false,"internalType":"string","name":"tokenImageUrl","type":"string"}],"name":"TokenDeployed","type":"event"},{"inputs":[],"name":"DEFAULT_ADMIN_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"DEPLOYER_ROLE","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"deployer","type":"address"}],"name":"addDeployer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"complianceImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"string","name":"name","type":"string"},{"internalType":"string","name":"symbol","type":"string"},{"internalType":"uint8","name":"decimals","type":"uint8"},{"internalType":"uint256","name":"maxSupply","type":"uint256"},{"internalType":"address","name":"admin","type":"address"},{"internalType":"string","name":"tokenPrice","type":"string"},{"internalType":"string","name":"bonusTiers","type":"string"},{"internalType":"string","name":"tokenDetails","type":"string"},{"internalType":"string","name":"tokenImageUrl","type":"string"}],"name":"deploySecurityToken","outputs":[{"internalType":"address","name":"tokenAddress","type":"address"},{"internalType":"address","name":"identityRegistryAddress","type":"address"},{"internalType":"address","name":"complianceAddress","type":"address"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"string","name":"name","type":"string"},{"internalType":"string","name":"symbol","type":"string"},{"internalType":"uint8","name":"decimals","type":"uint8"},{"internalType":"uint256","name":"maxSupply","type":"uint256"},{"internalType":"address","name":"admin","type":"address"},{"internalType":"string","name":"tokenPrice","type":"string"},{"internalType":"string","name":"bonusTiers","type":"string"},{"internalType":"string","name":"tokenDetails","type":"string"},{"internalType":"string","name":"tokenImageUrl","type":"string"},{"internalType":"bool","name":"withKYC","type":"bool"}],"name":"deploySecurityTokenWithOptions","outputs":[{"internalType":"address","name":"tokenAddress","type":"address"},{"internalType":"address","name":"identityRegistryAddress","type":"address"},{"internalType":"address","name":"complianceAddress","type":"address"}],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"uint256","name":"","type":"uint256"}],"name":"deployedTokens","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getAllDeployedTokens","outputs":[{"internalType":"address[]","name":"","type":"address[]"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"uint256","name":"index","type":"uint256"}],"name":"getDeployedToken","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"}],"name":"getRoleAdmin","outputs":[{"internalType":"bytes32","name":"","type":"bytes32"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"string","name":"symbol","type":"string"}],"name":"getTokenAddressBySymbol","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"getTokenCount","outputs":[{"internalType":"uint256","name":"","type":"uint256"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"grantRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"hasRole","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"deployer","type":"address"}],"name":"removeDeployer","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"callerConfirmation","type":"address"}],"name":"renounceRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[{"internalType":"bytes32","name":"role","type":"bytes32"},{"internalType":"address","name":"account","type":"address"}],"name":"revokeRole","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"securityTokenImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"bytes4","name":"interfaceId","type":"bytes4"}],"name":"supportsInterface","outputs":[{"internalType":"bool","name":"","type":"bool"}],"stateMutability":"view","type":"function"},{"inputs":[{"internalType":"address","name":"newTokenImplementation","type":"address"},{"internalType":"address","name":"newWhitelistImplementation","type":"address"},{"internalType":"address","name":"newWhitelistWithKYCImplementation","type":"address"},{"internalType":"address","name":"newComplianceImplementation","type":"address"}],"name":"updateImplementations","outputs":[],"stateMutability":"nonpayable","type":"function"},{"inputs":[],"name":"whitelistImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"},{"inputs":[],"name":"whitelistWithKYCImplementation","outputs":[{"internalType":"address","name":"","type":"address"}],"stateMutability":"view","type":"function"}]}');

/***/ }),

/***/ "(rsc)/./src/lib/prisma.ts":
/*!***************************!*\
  !*** ./src/lib/prisma.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient({\n    log:  true ? [\n        'query',\n        'error',\n        'warn'\n    ] : 0\n});\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3ByaXNtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFOUMsTUFBTUMsa0JBQWtCQztBQUlqQixNQUFNQyxTQUFTRixnQkFBZ0JFLE1BQU0sSUFBSSxJQUFJSCx3REFBWUEsQ0FBQztJQUMvREksS0FBS0MsS0FBc0MsR0FBRztRQUFDO1FBQVM7UUFBUztLQUFPLEdBQUcsQ0FBUztBQUN0RixHQUFHO0FBRUgsSUFBSUEsSUFBcUMsRUFBRUosZ0JBQWdCRSxNQUFNLEdBQUdBIiwic291cmNlcyI6WyJEOlxcZ2l0aHViXFx0b2tlbmRldi1uZXdyb29cXGFkbWluLXBhbmVsXFxzcmNcXGxpYlxccHJpc21hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFByaXNtYUNsaWVudCB9IGZyb20gJ0BwcmlzbWEvY2xpZW50JztcclxuXHJcbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XHJcbiAgcHJpc21hOiBQcmlzbWFDbGllbnQgfCB1bmRlZmluZWQ7XHJcbn07XHJcblxyXG5leHBvcnQgY29uc3QgcHJpc21hID0gZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA/PyBuZXcgUHJpc21hQ2xpZW50KHtcclxuICBsb2c6IHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAnZGV2ZWxvcG1lbnQnID8gWydxdWVyeScsICdlcnJvcicsICd3YXJuJ10gOiBbJ2Vycm9yJ10sXHJcbn0pO1xyXG5cclxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIGdsb2JhbEZvclByaXNtYS5wcmlzbWEgPSBwcmlzbWE7XHJcbiJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwibG9nIiwicHJvY2VzcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/prisma.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftokens%2Froute&page=%2Fapi%2Ftokens%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftokens%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();