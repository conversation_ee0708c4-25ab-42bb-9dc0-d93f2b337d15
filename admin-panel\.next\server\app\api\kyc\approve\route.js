/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/kyc/approve/route";
exports.ids = ["app/api/kyc/approve/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fapprove%2Froute&page=%2Fapi%2Fkyc%2Fapprove%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fapprove%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fapprove%2Froute&page=%2Fapi%2Fkyc%2Fapprove%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fapprove%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_github_tokendev_newroo_admin_panel_src_app_api_kyc_approve_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/kyc/approve/route.ts */ \"(rsc)/./src/app/api/kyc/approve/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/kyc/approve/route\",\n        pathname: \"/api/kyc/approve\",\n        filename: \"route\",\n        bundlePath: \"app/api/kyc/approve/route\"\n    },\n    resolvedPagePath: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\api\\\\kyc\\\\approve\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_github_tokendev_newroo_admin_panel_src_app_api_kyc_approve_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fapprove%2Froute&page=%2Fapi%2Fkyc%2Fapprove%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fapprove%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/kyc/approve/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/kyc/approve/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/address/checks.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/providers/provider-jsonrpc.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/wallet/wallet.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(rsc)/./node_modules/ethers/lib.esm/contract/contract.js\");\n\n\nconst SECURITY_TOKEN_CORE_ABI = [\n    \"function approveKYC(address user) external\",\n    \"function addToWhitelist(address account) external\"\n];\nconst KYC_CLAIMS_MODULE_ABI = [\n    \"function approveKYC(address token, address user) external\",\n    \"function issueKYCClaim(address user, bytes calldata data) external returns (bytes32)\"\n];\nasync function POST(request) {\n    try {\n        const { tokenAddress, userAddress } = await request.json();\n        if (!tokenAddress || !userAddress) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Token address and user address are required'\n            }, {\n                status: 400\n            });\n        }\n        // Validate addresses\n        if (!ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(tokenAddress) || !ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(userAddress)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid address format'\n            }, {\n                status: 400\n            });\n        }\n        // Get environment variables\n        const rpcUrl = process.env.AMOY_RPC_URL;\n        const privateKey = process.env.CONTRACT_ADMIN_PRIVATE_KEY;\n        const kycModuleAddress = process.env.AMOY_KYC_CLAIMS_MODULE_ADDRESS;\n        if (!rpcUrl || !privateKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Server configuration error'\n            }, {\n                status: 500\n            });\n        }\n        // Setup provider and signer\n        const provider = new ethers__WEBPACK_IMPORTED_MODULE_2__.JsonRpcProvider(rpcUrl);\n        const signer = new ethers__WEBPACK_IMPORTED_MODULE_3__.Wallet(privateKey, provider);\n        let txHash;\n        if (kycModuleAddress && ethers__WEBPACK_IMPORTED_MODULE_1__.isAddress(kycModuleAddress)) {\n            // Use KYC Claims Module if available\n            console.log('Using KYC Claims Module for KYC approval');\n            const kycModule = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(kycModuleAddress, KYC_CLAIMS_MODULE_ABI, signer);\n            try {\n                const tx = await kycModule.approveKYC(tokenAddress, userAddress);\n                await tx.wait();\n                txHash = tx.hash;\n                console.log('KYC approved via KYC Claims Module:', txHash);\n            } catch (moduleError) {\n                console.log('KYC Claims Module failed, falling back to direct token call:', moduleError);\n                // Fallback to direct token call\n                const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);\n                const tx = await tokenContract.approveKYC(userAddress);\n                await tx.wait();\n                txHash = tx.hash;\n                console.log('KYC approved via direct token call:', txHash);\n            }\n        } else {\n            // Direct token call\n            console.log('Using direct token call for KYC approval');\n            const tokenContract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(tokenAddress, SECURITY_TOKEN_CORE_ABI, signer);\n            const tx = await tokenContract.approveKYC(userAddress);\n            await tx.wait();\n            txHash = tx.hash;\n            console.log('KYC approved via direct token call:', txHash);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'KYC approved successfully',\n            txHash,\n            userAddress,\n            tokenAddress\n        });\n    } catch (error) {\n        console.error('Error approving KYC:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: `Failed to approve KYC: ${error.message}`\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/kyc/approve/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/ethers","vendor-chunks/@noble","vendor-chunks/@adraffy","vendor-chunks/aes-js"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkyc%2Fapprove%2Froute&page=%2Fapi%2Fkyc%2Fapprove%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkyc%2Fapprove%2Froute.ts&appDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5Cgithub%5Ctokendev-newroo%5Cadmin-panel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();