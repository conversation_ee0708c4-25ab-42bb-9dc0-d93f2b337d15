"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/page",{

/***/ "(app-pages-browser)/./src/app/modular-tokens/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/modular-tokens/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ModularTokensPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useModularToken__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../hooks/useModularToken */ \"(app-pages-browser)/./src/hooks/useModularToken.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// Import custom hook\n\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"0xbC7CfcC7ffB09747a381ed82369589706159a255\";\nconst UPGRADE_MANAGER_ADDRESS = \"0xb7a53dC340C2E5e823002B174629e2a44B8ed815\";\nfunction ModularTokensPage() {\n    _s();\n    // Use the custom hook for modular token functionality\n    const { provider, signer, tokenInfo, upgradeInfo, pendingUpgrades, upgradeHistory, loading, error, initializeProvider, mintTokens, togglePause, scheduleUpgrade, toggleEmergencyMode, refreshData, setError, updateTokenPrice, updateBonusTiers, updateMaxSupply, addToWhitelist, removeFromWhitelist } = (0,_hooks_useModularToken__WEBPACK_IMPORTED_MODULE_2__.useModularToken)();\n    // Local state for UI\n    const [success, setSuccess] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [mintAmount, setMintAmount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [mintAddress, setMintAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [upgradeDescription, setUpgradeDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newImplementationAddress, setNewImplementationAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('overview');\n    // KYC & Claims state\n    const [kycUserAddress, setKycUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [claimUserAddress, setClaimUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [claimTopicId, setClaimTopicId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('10101010000001');\n    const [claimData, setClaimData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [checkUserAddress, setCheckUserAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [verificationStatus, setVerificationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Admin Controls form states\n    const [newTokenPrice, setNewTokenPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newBonusTiers, setNewBonusTiers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [newMaxSupply, setNewMaxSupply] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [whitelistAddress, setWhitelistAddress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Clear success message after 5 seconds\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokensPage.useEffect\": ()=>{\n            if (success) {\n                const timer = setTimeout({\n                    \"ModularTokensPage.useEffect.timer\": ()=>setSuccess(null)\n                }[\"ModularTokensPage.useEffect.timer\"], 5000);\n                return ({\n                    \"ModularTokensPage.useEffect\": ()=>clearTimeout(timer)\n                })[\"ModularTokensPage.useEffect\"];\n            }\n        }\n    }[\"ModularTokensPage.useEffect\"], [\n        success\n    ]);\n    // Clear error message after 10 seconds\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ModularTokensPage.useEffect\": ()=>{\n            if (error) {\n                const timer = setTimeout({\n                    \"ModularTokensPage.useEffect.timer\": ()=>setError(null)\n                }[\"ModularTokensPage.useEffect.timer\"], 10000);\n                return ({\n                    \"ModularTokensPage.useEffect\": ()=>clearTimeout(timer)\n                })[\"ModularTokensPage.useEffect\"];\n            }\n        }\n    }[\"ModularTokensPage.useEffect\"], [\n        error,\n        setError\n    ]);\n    const handleMint = async ()=>{\n        if (!mintAddress || !mintAmount) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await mintTokens(mintAddress, mintAmount);\n            setSuccess(\"Successfully minted \".concat(mintAmount, \" tokens to \").concat(mintAddress));\n            setMintAmount('');\n            setMintAddress('');\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error minting tokens:', error);\n            setError(\"Failed to mint tokens: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handlePauseToggle = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await togglePause();\n            setSuccess(\"Token \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpaused' : 'paused', \" successfully\"));\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error toggling pause:', error);\n            setError(\"Failed to \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" token: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handlePauseToggleAPI = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            const action = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause';\n            const response = await fetch('/api/admin/toggle-pause', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: SECURITY_TOKEN_CORE_ADDRESS,\n                    action: action\n                })\n            });\n            const result = await response.json();\n            if (!response.ok) {\n                throw new Error(result.error || 'API request failed');\n            }\n            if (result.alreadyInDesiredState) {\n                setSuccess(result.message);\n            } else {\n                setSuccess(\"\".concat(result.message, \" (Tx: \").concat(result.transactionHash, \")\"));\n            }\n            await refreshData(); // Refresh token info\n        } catch (error) {\n            console.error('Error with API pause toggle:', error);\n            setError(\"API \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" failed: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleScheduleUpgrade = async ()=>{\n        if (!newImplementationAddress || !upgradeDescription) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await scheduleUpgrade(newImplementationAddress, upgradeDescription);\n            setSuccess('Upgrade scheduled successfully! It will be executable after the 48-hour timelock.');\n            setNewImplementationAddress('');\n            setUpgradeDescription('');\n            await refreshData(); // Refresh upgrade info\n        } catch (error) {\n            console.error('Error scheduling upgrade:', error);\n            setError(\"Failed to schedule upgrade: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleEmergencyModeToggle = async ()=>{\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            await toggleEmergencyMode();\n            setSuccess(\"Emergency mode \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'deactivated' : 'activated', \" successfully\"));\n            await refreshData(); // Refresh upgrade info\n        } catch (error) {\n            console.error('Error toggling emergency mode:', error);\n            setError(\"Failed to \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'deactivate' : 'activate', \" emergency mode: \").concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // KYC & Claims handlers\n    const handleApproveKYC = async ()=>{\n        if (!kycUserAddress) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Call the KYC approval API\n            const response = await fetch('/api/kyc/approve', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: SECURITY_TOKEN_CORE_ADDRESS,\n                    userAddress: kycUserAddress\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to approve KYC');\n            }\n            setSuccess(\"KYC approved successfully for \".concat(kycUserAddress));\n            setKycUserAddress('');\n        } catch (error) {\n            console.error('Error approving KYC:', error);\n            setError(\"Failed to approve KYC: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleAddToWhitelist = async ()=>{\n        if (!kycUserAddress) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Call the whitelist API\n            const response = await fetch('/api/kyc/whitelist', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    tokenAddress: SECURITY_TOKEN_CORE_ADDRESS,\n                    userAddress: kycUserAddress\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to add to whitelist');\n            }\n            setSuccess(\"User added to whitelist successfully: \".concat(kycUserAddress));\n            setKycUserAddress('');\n        } catch (error) {\n            console.error('Error adding to whitelist:', error);\n            setError(\"Failed to add to whitelist: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleIssueClaim = async ()=>{\n        if (!claimUserAddress || !claimTopicId) return;\n        setActionLoading(true);\n        setError(null);\n        setSuccess(null);\n        try {\n            // Call the claims API\n            const response = await fetch('/api/kyc/issue-claim', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    userAddress: claimUserAddress,\n                    topicId: claimTopicId,\n                    data: claimData || ''\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to issue claim');\n            }\n            const result = await response.json();\n            setSuccess(\"Claim issued successfully! Claim ID: \".concat(result.claimId));\n            setClaimUserAddress('');\n            setClaimData('');\n        } catch (error) {\n            console.error('Error issuing claim:', error);\n            setError(\"Failed to issue claim: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleCheckStatus = async ()=>{\n        if (!checkUserAddress) return;\n        setActionLoading(true);\n        setError(null);\n        try {\n            // Call the status check API\n            const response = await fetch(\"/api/kyc/status?tokenAddress=\".concat(SECURITY_TOKEN_CORE_ADDRESS, \"&userAddress=\").concat(checkUserAddress));\n            if (!response.ok) {\n                throw new Error('Failed to check status');\n            }\n            const status = await response.json();\n            setVerificationStatus(status);\n        } catch (error) {\n            console.error('Error checking status:', error);\n            setError(\"Failed to check status: \".concat(error.message));\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Admin Controls Handlers\n    const handleUpdatePrice = async ()=>{\n        if (!newTokenPrice) return;\n        setActionLoading(true);\n        try {\n            await updateTokenPrice(newTokenPrice);\n            setSuccess(\"Token price updated to \".concat(newTokenPrice));\n            setNewTokenPrice('');\n            await refreshData();\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleUpdateBonusTiers = async ()=>{\n        if (!newBonusTiers) return;\n        setActionLoading(true);\n        try {\n            await updateBonusTiers(newBonusTiers);\n            setSuccess(\"Bonus tiers updated to: \".concat(newBonusTiers));\n            setNewBonusTiers('');\n            await refreshData();\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleUpdateMaxSupply = async ()=>{\n        if (!newMaxSupply) return;\n        setActionLoading(true);\n        try {\n            await updateMaxSupply(newMaxSupply);\n            setSuccess(\"Max supply updated to \".concat(newMaxSupply));\n            setNewMaxSupply('');\n            await refreshData();\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleAddToWhitelistAdmin = async ()=>{\n        if (!whitelistAddress) return;\n        setActionLoading(true);\n        try {\n            await addToWhitelist(whitelistAddress);\n            setSuccess(\"Address \".concat(whitelistAddress, \" added to whitelist\"));\n            setWhitelistAddress('');\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleRemoveFromWhitelist = async ()=>{\n        if (!whitelistAddress) return;\n        setActionLoading(true);\n        try {\n            await removeFromWhitelist(whitelistAddress);\n            setSuccess(\"Address \".concat(whitelistAddress, \" removed from whitelist\"));\n            setWhitelistAddress('');\n        } catch (err) {\n            setError(err.message);\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    // Utility functions\n    const formatAddress = (address)=>{\n        return \"\".concat(address.slice(0, 6), \"...\").concat(address.slice(-4));\n    };\n    const formatTimestamp = (timestamp)=>{\n        return new Date(timestamp * 1000).toLocaleString();\n    };\n    const getTimeUntilExecution = (executeTime)=>{\n        const now = Math.floor(Date.now() / 1000);\n        const timeLeft = executeTime - now;\n        if (timeLeft <= 0) return 'Ready to execute';\n        const hours = Math.floor(timeLeft / 3600);\n        const minutes = Math.floor(timeLeft % 3600 / 60);\n        return \"\".concat(hours, \"h \").concat(minutes, \"m remaining\");\n    };\n    if (!provider || !signer) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto p-6\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white rounded-lg shadow p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-2\",\n                        children: \"Connect Wallet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 mb-4\",\n                        children: \"Please connect your MetaMask wallet to manage modular tokens.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: initializeProvider,\n                        className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded\",\n                        children: \"Connect Wallet\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 424,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n            lineNumber: 423,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto p-6 space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold\",\n                                children: \"Modular Token Management\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Manage your modular ERC-3643 security tokens and upgrades\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 445,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'),\n                                children: (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? \"Emergency Mode Active\" : \"Normal Mode\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 11\n                            }, this),\n                            tokenInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(tokenInfo.paused ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                children: tokenInfo.paused ? \"Paused\" : \"Active\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: refreshData,\n                                disabled: loading,\n                                className: \"bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-1 px-3 rounded text-sm disabled:opacity-50\",\n                                children: [\n                                    loading ? '🔄' : '↻',\n                                    \" Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 449,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 442,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-red-600 mr-2\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 479,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 480,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                    lineNumber: 478,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 477,\n                columnNumber: 9\n            }, this),\n            success && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-green-600 mr-2\",\n                            children: \"✅\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 488,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: success\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 489,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                    lineNumber: 487,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 486,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"-mb-px flex space-x-8\",\n                            children: [\n                                'overview',\n                                'operations',\n                                'admin-controls',\n                                'kyc-claims',\n                                'upgrades',\n                                'history'\n                            ].map((tab)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setActiveTab(tab),\n                                    className: \"py-2 px-1 border-b-2 font-medium text-sm \".concat(activeTab === tab ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'),\n                                    children: tab === 'kyc-claims' ? 'KYC & Claims' : tab === 'admin-controls' ? 'Admin Controls' : tab.charAt(0).toUpperCase() + tab.slice(1).replace('-', ' ')\n                                }, tab, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 497,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 496,\n                        columnNumber: 9\n                    }, this),\n                    activeTab === 'overview' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: \"Token Information\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Current token details and status\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: tokenInfo ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Name:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 527,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 528,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Symbol:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 531,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.symbol\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 530,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Version:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.version\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 536,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 534,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Total Supply:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 539,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.totalSupply\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 540,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 538,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Max Supply:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.maxSupply\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Decimals:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 547,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: tokenInfo.decimals\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: \"Status:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 551,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(tokenInfo.paused ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                                    children: tokenInfo.paused ? \"Paused\" : \"Active\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 552,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Loading token information...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 564,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 520,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: \"Contract Addresses\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 571,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Deployed contract addresses on Amoy testnet\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 572,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"SecurityTokenCore:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 font-mono\",\n                                                                children: SECURITY_TOKEN_CORE_ADDRESS\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 576,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"UpgradeManager:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 581,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500 font-mono\",\n                                                                children: UPGRADE_MANAGER_ADDRESS\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 582,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 580,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"pt-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>window.open(\"https://amoy.polygonscan.com/address/\".concat(SECURITY_TOKEN_CORE_ADDRESS), '_blank'),\n                                                            className: \"bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-1 px-3 rounded text-sm\",\n                                                            children: \"View on PolygonScan\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 587,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 586,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 570,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 519,\n                                columnNumber: 13\n                            }, this),\n                            (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.metadata) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mb-2\",\n                                        children: \"Token Metadata\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 600,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"Additional token information and configuration\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 601,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Price:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: tokenInfo.metadata.tokenPrice\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 603,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Bonus Tiers:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: tokenInfo.metadata.bonusTiers\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 609,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Details:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 612,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500 mt-1\",\n                                                        children: tokenInfo.metadata.tokenDetails\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 613,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 599,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 518,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'operations' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"Mint Tokens\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 627,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Mint new tokens to a specified address\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 628,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"mintAddress\",\n                                                            className: \"block text-sm font-medium text-gray-700\",\n                                                            children: \"Recipient Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 631,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"mintAddress\",\n                                                            type: \"text\",\n                                                            placeholder: \"0x...\",\n                                                            value: mintAddress,\n                                                            onChange: (e)=>setMintAddress(e.target.value),\n                                                            className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 632,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 630,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"mintAmount\",\n                                                            className: \"block text-sm font-medium text-gray-700\",\n                                                            children: \"Amount\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 642,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"mintAmount\",\n                                                            type: \"number\",\n                                                            placeholder: \"1000\",\n                                                            value: mintAmount,\n                                                            onChange: (e)=>setMintAmount(e.target.value),\n                                                            className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 643,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 641,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleMint,\n                                                    disabled: actionLoading || !mintAddress || !mintAmount,\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                    children: [\n                                                        actionLoading ? '🔄 ' : '',\n                                                        \"Mint Tokens\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 652,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 629,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 626,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"Token Controls\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 663,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Pause/unpause token transfers and emergency controls\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handlePauseToggle,\n                                                    disabled: actionLoading,\n                                                    className: \"w-full font-bold py-2 px-4 rounded disabled:opacity-50 \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-red-600 hover:bg-red-700 text-white'),\n                                                    children: [\n                                                        actionLoading ? '🔄 ' : '',\n                                                        (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'Unpause Token (Wallet)' : 'Pause Token (Wallet)'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handlePauseToggleAPI,\n                                                    disabled: actionLoading,\n                                                    className: \"w-full font-bold py-2 px-4 rounded disabled:opacity-50 border-2 \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'bg-green-500 hover:bg-green-600 text-white border-green-300' : 'bg-red-500 hover:bg-red-600 text-white border-red-300'),\n                                                    children: [\n                                                        actionLoading ? '🔄 ' : '🔧 ',\n                                                        (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'Unpause Token (API)' : 'Pause Token (API)'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 678,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500 text-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Try API option if wallet method fails\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 691,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 690,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleEmergencyModeToggle,\n                                                    disabled: actionLoading,\n                                                    className: \"w-full font-bold py-2 px-4 rounded disabled:opacity-50 \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-red-600 hover:bg-red-700 text-white'),\n                                                    children: [\n                                                        actionLoading ? '🔄 ' : '',\n                                                        (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: \"✅ Deactivate Emergency Mode\"\n                                                        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: \"⚡ Activate Emergency Mode\"\n                                                        }, void 0, false)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 694,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 665,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 625,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 624,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'admin-controls' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-600 text-xl\",\n                                                children: \"⚙️\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 721,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 720,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-green-800\",\n                                                    children: \"Complete Admin Controls - ALL WORKING\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 724,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-sm text-green-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Full administrative control over token parameters and functionality:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-2 mt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-green-600 mr-1\",\n                                                                            children: \"✅\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 731,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Price Updates\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 732,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 730,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-green-600 mr-1\",\n                                                                            children: \"✅\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 735,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Metadata Management\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 736,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 734,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-green-600 mr-1\",\n                                                                            children: \"✅\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 739,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Supply Management\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 740,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-green-600 mr-1\",\n                                                                            children: \"✅\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 743,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Pause/Unpause\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 744,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 742,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-green-600 mr-1\",\n                                                                            children: \"✅\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 747,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Whitelist Control\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 748,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 746,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-green-600 mr-1\",\n                                                                            children: \"✅\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 751,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Token Minting\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 752,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 750,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 729,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 727,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 719,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 718,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: [\n                                                    \"Price & Metadata Management\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded\",\n                                                        children: \"✅ Working\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 764,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 762,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Update token price, bonus tiers, and metadata\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"newTokenPrice\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"Token Price\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 769,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"newTokenPrice\",\n                                                                type: \"text\",\n                                                                placeholder: \"e.g., 7.50 USD\",\n                                                                value: newTokenPrice,\n                                                                onChange: (e)=>setNewTokenPrice(e.target.value),\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 770,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"newBonusTiers\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"Bonus Tiers\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 780,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"newBonusTiers\",\n                                                                type: \"text\",\n                                                                placeholder: \"e.g., Early: 45%, Standard: 30%, Late: 15%\",\n                                                                value: newBonusTiers,\n                                                                onChange: (e)=>setNewBonusTiers(e.target.value),\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 781,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 779,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleUpdatePrice,\n                                                                disabled: actionLoading || !newTokenPrice,\n                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                                children: [\n                                                                    actionLoading ? '🔄 ' : '💰 ',\n                                                                    \"Update Price\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 791,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleUpdateBonusTiers,\n                                                                disabled: actionLoading || !newBonusTiers,\n                                                                className: \"bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                                children: [\n                                                                    actionLoading ? '🔄 ' : '🎯 ',\n                                                                    \"Update Tiers\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 798,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 790,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 767,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 761,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: [\n                                                    \"Supply Management\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded\",\n                                                        children: \"✅ Working\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 812,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 810,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Manage token supply and minting\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 814,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"newMaxSupply\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"New Max Supply\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 817,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"newMaxSupply\",\n                                                                type: \"number\",\n                                                                placeholder: \"e.g., 100000000\",\n                                                                value: newMaxSupply,\n                                                                onChange: (e)=>setNewMaxSupply(e.target.value),\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 818,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 816,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"mintToAddress\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"Mint To Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 828,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"mintToAddress\",\n                                                                type: \"text\",\n                                                                placeholder: \"0x...\",\n                                                                value: mintAddress,\n                                                                onChange: (e)=>setMintAddress(e.target.value),\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 829,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 827,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"mintAmountAdmin\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"Amount to Mint\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 839,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"mintAmountAdmin\",\n                                                                type: \"number\",\n                                                                placeholder: \"e.g., 1000\",\n                                                                value: mintAmount,\n                                                                onChange: (e)=>setMintAmount(e.target.value),\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 840,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 838,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleUpdateMaxSupply,\n                                                                disabled: actionLoading || !newMaxSupply,\n                                                                className: \"bg-orange-600 hover:bg-orange-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                                children: [\n                                                                    actionLoading ? '🔄 ' : '📈 ',\n                                                                    \"Update Supply\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 850,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleMint,\n                                                                disabled: actionLoading || !mintAddress || !mintAmount,\n                                                                className: \"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                                children: [\n                                                                    actionLoading ? '🔄 ' : '🪙 ',\n                                                                    \"Mint Tokens\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 857,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 849,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 815,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 809,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 760,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: [\n                                                    \"Token Controls\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded\",\n                                                        children: \"✅ Working\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 873,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 871,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Pause/unpause token transfers and emergency controls\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 875,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handlePauseToggle,\n                                                                disabled: actionLoading,\n                                                                className: \"font-bold py-2 px-4 rounded disabled:opacity-50 \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-red-600 hover:bg-red-700 text-white'),\n                                                                children: [\n                                                                    actionLoading ? '🔄 ' : (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? '▶️ ' : '⏸️ ',\n                                                                    (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'Unpause Token (Wallet)' : 'Pause Token (Wallet)'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 878,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handlePauseToggleAPI,\n                                                                disabled: actionLoading,\n                                                                className: \"font-bold py-2 px-4 rounded disabled:opacity-50 \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'bg-green-500 hover:bg-green-600 text-white border-2 border-green-300' : 'bg-red-500 hover:bg-red-600 text-white border-2 border-red-300'),\n                                                                children: [\n                                                                    actionLoading ? '🔄 ' : (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? '🔧 ' : '🔧 ',\n                                                                    (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'Unpause Token (API)' : 'Pause Token (API)'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 890,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 877,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Wallet:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 904,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" Uses your connected wallet (may fail on Amoy testnet)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 904,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"API:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 905,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" Uses server-side transaction (more reliable)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 905,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 903,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 gap-2\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleEmergencyModeToggle,\n                                                            disabled: actionLoading,\n                                                            className: \"font-bold py-2 px-4 rounded disabled:opacity-50 \".concat((upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'bg-green-600 hover:bg-green-700 text-white' : 'bg-orange-600 hover:bg-orange-700 text-white'),\n                                                            children: [\n                                                                actionLoading ? '🔄 ' : (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? '✅ ' : '⚡ ',\n                                                                (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? 'Deactivate Emergency' : 'Activate Emergency'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 908,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 907,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Current Status:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 922,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'text-red-600' : 'text-green-600',\n                                                                        children: (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? ' Paused' : ' Active'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 923,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 922,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Total Supply:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 927,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.totalSupply) || 'Loading...'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 927,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Max Supply:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 928,\n                                                                        columnNumber: 24\n                                                                    }, this),\n                                                                    \" \",\n                                                                    (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.maxSupply) || 'Loading...'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 928,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 921,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 876,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 870,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: [\n                                                    \"Whitelist Management\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\",\n                                                        children: \"Direct Access\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 936,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 934,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Direct whitelist management (bypasses modules)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 938,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"whitelistAddress\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 941,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"whitelistAddress\",\n                                                                type: \"text\",\n                                                                placeholder: \"0x...\",\n                                                                value: whitelistAddress,\n                                                                onChange: (e)=>setWhitelistAddress(e.target.value),\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 942,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 940,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleAddToWhitelistAdmin,\n                                                                disabled: actionLoading || !whitelistAddress,\n                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                                children: [\n                                                                    actionLoading ? '🔄 ' : '✅ ',\n                                                                    \"Add to Whitelist\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 952,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleRemoveFromWhitelist,\n                                                                disabled: actionLoading || !whitelistAddress,\n                                                                className: \"bg-red-600 hover:bg-red-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                                children: [\n                                                                    actionLoading ? '🔄 ' : '❌ ',\n                                                                    \"Remove from Whitelist\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 959,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 951,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 939,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 933,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 869,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-600 text-xl\",\n                                                children: \"\\uD83D\\uDCA1\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 974,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 973,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-blue-800\",\n                                                    children: \"Admin Functions Available\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 977,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-sm text-blue-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"All admin functions are now available and tested working:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 981,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"mt-1 list-disc list-inside space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Price Management:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 983,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \" updateTokenPrice(), updateBonusTiers()\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 983,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Supply Control:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 984,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \" updateMaxSupply(), mint()\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 984,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Token Control:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 985,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \" pause(), unpause()\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 985,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Access Control:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 986,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \" addToWhitelist(), removeFromWhitelist()\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 986,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Emergency:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 987,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \" forcedTransfer(), burn()\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 987,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 982,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 980,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 976,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 972,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 971,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 717,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'kyc-claims' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-600 text-xl\",\n                                                children: \"\\uD83D\\uDD10\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 1001,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 1000,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-blue-800\",\n                                                    children: \"On-Chain KYC & Claims System\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 1004,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-sm text-blue-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"Manage KYC verification and custom claims using Tokeny-style Topic IDs:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 1008,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"mt-1 list-disc list-inside space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"KYC Verification:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1010,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \" Topic ID 10101010000001\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1010,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"General Qualification:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1011,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \" Topic ID 10101010000004\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1011,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Accredited Investor:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1012,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \" Topic ID 10101010000002\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1012,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                            children: \"Custom Claims:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1013,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \" Like 10101010000648 for specific KYC status\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1013,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 1009,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 1007,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 1003,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 999,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 998,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-green-600 text-xl\",\n                                                children: \"\\uD83C\\uDF89\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 1023,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 1022,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-green-800\",\n                                                    children: \"System Status - FULLY OPERATIONAL\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 1026,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-sm text-green-700\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-2 mt-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-green-600 mr-1\",\n                                                                            children: \"✅\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1032,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Claims Issuance\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1033,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1031,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-green-600 mr-1\",\n                                                                            children: \"✅\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1036,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Status Checking\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1037,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1035,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-green-600 mr-1\",\n                                                                            children: \"✅\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1040,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"On-Chain Verification\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                            lineNumber: 1041,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1039,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 1030,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"mt-2 text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                    children: \"New Token Deployed:\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1045,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \" 0x1201Ff51382A5FCE89b1fBbEa970D81C9357509D with full admin access and KYC Claims Module registered!\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 1044,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 1029,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 1025,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 1020,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-blue-600 text-xl\",\n                                                children: \"\\uD83D\\uDE80\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 1055,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 1054,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-blue-800\",\n                                                    children: \"Quick Start Guide\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 1058,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mt-1 text-sm text-blue-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                                        className: \"list-decimal list-inside space-y-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Issue Claims:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1063,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \" Use the Custom Claims section below (fully working)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1063,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Check Status:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1064,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \" Use the Verification Status Checker (fully working)\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1064,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"View Transactions:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1065,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \" All claims are recorded on-chain and verifiable\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1065,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                                        children: \"Cross-Token Usage:\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1066,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \" Claims work across multiple tokens\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1066,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 1062,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 1061,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 1057,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 1053,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 1052,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: [\n                                                    \"KYC Management\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded\",\n                                                        children: \"Use Claims Instead\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 1077,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 1075,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"For KYC approval, use the Custom Claims section below with Topic ID 10101010000001\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 1079,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"kycUserAddress\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"User Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1082,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"kycUserAddress\",\n                                                                type: \"text\",\n                                                                placeholder: \"0x...\",\n                                                                value: kycUserAddress,\n                                                                onChange: (e)=>setKycUserAddress(e.target.value),\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1083,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 1081,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleApproveKYC,\n                                                                disabled: actionLoading || !kycUserAddress,\n                                                                className: \"bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                                children: [\n                                                                    actionLoading ? '🔄 ' : '✅ ',\n                                                                    \"Approve KYC\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1093,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: handleAddToWhitelist,\n                                                                disabled: actionLoading || !kycUserAddress,\n                                                                className: \"bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                                children: [\n                                                                    actionLoading ? '🔄 ' : '📋 ',\n                                                                    \"Add to Whitelist\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1100,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 1092,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 1080,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 1074,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white rounded-lg shadow p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-2\",\n                                                children: [\n                                                    \"Custom Claims\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded\",\n                                                        children: \"✅ Working\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 1114,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 1112,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 mb-4\",\n                                                children: \"Issue custom claims with specific Topic IDs (fully operational)\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 1116,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"claimUserAddress\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"User Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1119,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"claimUserAddress\",\n                                                                type: \"text\",\n                                                                placeholder: \"0x...\",\n                                                                value: claimUserAddress,\n                                                                onChange: (e)=>setClaimUserAddress(e.target.value),\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1120,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 1118,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"claimTopicId\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"Topic ID\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1130,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                id: \"claimTopicId\",\n                                                                value: claimTopicId,\n                                                                onChange: (e)=>setClaimTopicId(e.target.value),\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"10101010000001\",\n                                                                        children: \"10101010000001 - KYC Verification\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1137,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"10101010000002\",\n                                                                        children: \"10101010000002 - Accredited Investor\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1138,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"10101010000003\",\n                                                                        children: \"10101010000003 - Jurisdiction Compliance\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1139,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"10101010000004\",\n                                                                        children: \"10101010000004 - General Qualification\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1140,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                        value: \"10101010000648\",\n                                                                        children: \"10101010000648 - Custom KYC Status\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1141,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1131,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 1129,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"claimData\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"Claim Data (Optional)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1145,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                id: \"claimData\",\n                                                                placeholder: \"Additional claim information...\",\n                                                                value: claimData,\n                                                                onChange: (e)=>setClaimData(e.target.value),\n                                                                rows: 2,\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1146,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 1144,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleIssueClaim,\n                                                        disabled: actionLoading || !claimUserAddress || !claimTopicId,\n                                                        className: \"w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                        children: [\n                                                            actionLoading ? '🔄 ' : '🏷️ ',\n                                                            \"Issue Custom Claim\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 1155,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 1117,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 1111,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 1073,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mb-2\",\n                                        children: [\n                                            \"Verification Status Checker\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded\",\n                                                children: \"✅ Working\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 1169,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 1167,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"Check KYC and claims status for any user (fully operational)\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 1171,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                htmlFor: \"checkUserAddress\",\n                                                                className: \"block text-sm font-medium text-gray-700\",\n                                                                children: \"User Address\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1175,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                id: \"checkUserAddress\",\n                                                                type: \"text\",\n                                                                placeholder: \"0x...\",\n                                                                value: checkUserAddress,\n                                                                onChange: (e)=>setCheckUserAddress(e.target.value),\n                                                                className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1176,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 1174,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-end\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: handleCheckStatus,\n                                                            disabled: actionLoading || !checkUserAddress,\n                                                            className: \"bg-gray-600 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                            children: [\n                                                                actionLoading ? '🔄 ' : '🔍 ',\n                                                                \"Check Status\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 1186,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 1185,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 1173,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg p-4 bg-gray-50\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: verificationStatus ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"font-medium mb-2\",\n                                                                children: [\n                                                                    \"Verification Status for \",\n                                                                    checkUserAddress.slice(0, 10),\n                                                                    \"...\",\n                                                                    checkUserAddress.slice(-8)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1201,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"KYC Approved:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 1204,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: verificationStatus.kycApproved ? 'text-green-600 font-medium' : 'text-red-600',\n                                                                                children: verificationStatus.kycApproved ? '✅ Yes' : '❌ No'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 1205,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1203,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Whitelisted:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 1210,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: verificationStatus.whitelisted ? 'text-green-600 font-medium' : 'text-red-600',\n                                                                                children: verificationStatus.whitelisted ? '✅ Yes' : '❌ No'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 1211,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1209,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Eligible:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 1216,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: verificationStatus.eligible ? 'text-green-600 font-medium' : 'text-red-600',\n                                                                                children: verificationStatus.eligible ? '✅ Yes' : '❌ No'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 1217,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1215,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Method:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 1222,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-blue-600 font-medium\",\n                                                                                children: verificationStatus.method\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 1223,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1221,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1202,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Status results will appear here after checking a user address.\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1229,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"KYC Approved:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 1232,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-400\",\n                                                                                children: \"-\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 1233,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1231,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Whitelisted:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 1236,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-400\",\n                                                                                children: \"-\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 1237,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1235,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Eligible:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 1240,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-400\",\n                                                                                children: \"-\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 1241,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1239,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Method:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 1244,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-gray-400\",\n                                                                                children: \"-\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                                lineNumber: 1245,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1243,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1230,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 1198,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 1197,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 1172,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 1166,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white rounded-lg shadow p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium mb-2\",\n                                        children: \"Token Claims Configuration\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 1256,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mb-4\",\n                                        children: \"Configure which claims are required for this token\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 1257,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    className: \"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1262,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-sm\",\n                                                                    children: \"Enable KYC Verification\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1263,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 1261,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 1260,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    className: \"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1268,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"ml-2 text-sm\",\n                                                                    children: \"Enable Claims System\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1269,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 1267,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 1266,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 1259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-sm font-medium text-gray-700 mb-2\",\n                                                        children: \"Required Claims\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 1275,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        className: \"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1278,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-sm\",\n                                                                        children: \"KYC Verification (10101010000001)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1279,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1277,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        className: \"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1282,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-sm\",\n                                                                        children: \"General Qualification (10101010000004)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1283,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1281,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                className: \"flex items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"checkbox\",\n                                                                        className: \"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1286,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"ml-2 text-sm\",\n                                                                        children: \"Accredited Investor (10101010000002)\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                        lineNumber: 1287,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                lineNumber: 1285,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                        lineNumber: 1276,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 1274,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                disabled: actionLoading,\n                                                className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                children: [\n                                                    actionLoading ? '🔄 ' : '⚙️ ',\n                                                    \"Update Token Configuration\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                lineNumber: 1292,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                        lineNumber: 1258,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                lineNumber: 1255,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 997,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'upgrades' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-2 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"Schedule Upgrade\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 1307,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Schedule a new implementation upgrade with 48-hour timelock\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 1308,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"newImplementation\",\n                                                            className: \"block text-sm font-medium text-gray-700\",\n                                                            children: \"New Implementation Address\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 1313,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            id: \"newImplementation\",\n                                                            type: \"text\",\n                                                            placeholder: \"0x...\",\n                                                            value: newImplementationAddress,\n                                                            onChange: (e)=>setNewImplementationAddress(e.target.value),\n                                                            className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 1314,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 1312,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            htmlFor: \"upgradeDescription\",\n                                                            className: \"block text-sm font-medium text-gray-700\",\n                                                            children: \"Upgrade Description\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 1324,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            id: \"upgradeDescription\",\n                                                            placeholder: \"Describe the changes in this upgrade...\",\n                                                            value: upgradeDescription,\n                                                            onChange: (e)=>setUpgradeDescription(e.target.value),\n                                                            rows: 3,\n                                                            className: \"mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 1325,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 1323,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: handleScheduleUpgrade,\n                                                    disabled: actionLoading || !newImplementationAddress || !upgradeDescription,\n                                                    className: \"w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded disabled:opacity-50\",\n                                                    children: [\n                                                        actionLoading ? '🔄 ' : '⏰ ',\n                                                        \"Schedule Upgrade (48h Timelock)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 1334,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 1311,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 1306,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white rounded-lg shadow p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-medium mb-2\",\n                                            children: \"Pending Upgrades\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 1346,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 mb-4\",\n                                            children: \"Upgrades waiting for timelock expiration or execution\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 1347,\n                                            columnNumber: 17\n                                        }, this),\n                                        pendingUpgrades.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 text-center py-4\",\n                                            children: \"No pending upgrades\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 1351,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: pendingUpgrades.map((upgrade)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border rounded-lg p-3 space-y-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-start\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: upgrade.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1359,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(upgrade.executed ? 'bg-green-100 text-green-800' : upgrade.cancelled ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'),\n                                                                    children: upgrade.executed ? \"Executed\" : upgrade.cancelled ? \"Cancelled\" : \"Pending\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1362,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 1358,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-xs text-gray-500 space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        \"Implementation: \",\n                                                                        upgrade.newImplementation.slice(0, 10),\n                                                                        \"...\",\n                                                                        upgrade.newImplementation.slice(-8)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1373,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        \"Execute Time: \",\n                                                                        new Date(upgrade.executeTime * 1000).toLocaleString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1374,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: getTimeUntilExecution(upgrade.executeTime)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1375,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 1372,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, upgrade.upgradeId, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 1357,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 1355,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 1345,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 1305,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 1304,\n                        columnNumber: 11\n                    }, this),\n                    activeTab === 'history' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg shadow p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-medium mb-2\",\n                                    children: \"Upgrade History\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 1389,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"Complete history of all upgrades performed on this token\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 1390,\n                                    columnNumber: 15\n                                }, this),\n                                upgradeHistory.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 text-center py-4\",\n                                    children: \"No upgrade history available\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 1394,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: upgradeHistory.map((record, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-4 space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-between items-start\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"font-medium\",\n                                                                    children: record.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1403,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        new Date(record.timestamp * 1000).toLocaleString(),\n                                                                        \" by \",\n                                                                        record.executor.slice(0, 10),\n                                                                        \"...\",\n                                                                        record.executor.slice(-8)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1404,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 1402,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(record.isEmergency ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'),\n                                                                    children: record.isEmergency ? \"Emergency\" : \"Scheduled\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1409,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                record.version && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800\",\n                                                                    children: [\n                                                                        \"v\",\n                                                                        record.version\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                                    lineNumber: 1415,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 1408,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 1401,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"From: \",\n                                                                record.oldImplementation.slice(0, 10),\n                                                                \"...\",\n                                                                record.oldImplementation.slice(-8)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 1422,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                \"To: \",\n                                                                record.newImplementation.slice(0, 10),\n                                                                \"...\",\n                                                                record.newImplementation.slice(-8)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                            lineNumber: 1423,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                                    lineNumber: 1421,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                            lineNumber: 1400,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                                    lineNumber: 1398,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                            lineNumber: 1388,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                        lineNumber: 1387,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n                lineNumber: 494,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\github\\\\tokendev-newroo\\\\admin-panel\\\\src\\\\app\\\\modular-tokens\\\\page.tsx\",\n        lineNumber: 441,\n        columnNumber: 5\n    }, this);\n}\n_s(ModularTokensPage, \"ANXzhOg/jd/YJR5w0KuAM66p67c=\", false, function() {\n    return [\n        _hooks_useModularToken__WEBPACK_IMPORTED_MODULE_2__.useModularToken\n    ];\n});\n_c = ModularTokensPage;\nvar _c;\n$RefreshReg$(_c, \"ModularTokensPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/modular-tokens/page.tsx\n"));

/***/ })

});