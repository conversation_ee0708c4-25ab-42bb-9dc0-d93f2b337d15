"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/modular-tokens/page",{

/***/ "(app-pages-browser)/./src/hooks/useModularToken.ts":
/*!**************************************!*\
  !*** ./src/hooks/useModularToken.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useModularToken: () => (/* binding */ useModularToken)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/providers/provider-browser.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/contract/contract.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/units.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/crypto/keccak.js\");\n/* harmony import */ var ethers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ethers */ \"(app-pages-browser)/./node_modules/ethers/lib.esm/utils/utf8.js\");\n/* harmony import */ var _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/contracts/SecurityTokenCore.json */ \"(app-pages-browser)/./src/contracts/SecurityTokenCore.json\");\n/* harmony import */ var _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contracts/UpgradeManager.json */ \"(app-pages-browser)/./src/contracts/UpgradeManager.json\");\n/* __next_internal_client_entry_do_not_use__ useModularToken auto */ \n\n// Import ABIs\n\n\n// Contract addresses from environment\nconst SECURITY_TOKEN_CORE_ADDRESS = \"******************************************\";\nconst UPGRADE_MANAGER_ADDRESS = \"******************************************\";\nfunction useModularToken() {\n    const [provider, setProvider] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [signer, setSigner] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [tokenInfo, setTokenInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [upgradeInfo, setUpgradeInfo] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [pendingUpgrades, setPendingUpgrades] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [upgradeHistory, setUpgradeHistory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    // Initialize provider and signer\n    const initializeProvider = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[initializeProvider]\": async ()=>{\n            try {\n                if ( true && window.ethereum) {\n                    const provider = new ethers__WEBPACK_IMPORTED_MODULE_3__.BrowserProvider(window.ethereum);\n                    await provider.send('eth_requestAccounts', []);\n                    const signer = await provider.getSigner();\n                    setProvider(provider);\n                    setSigner(signer);\n                    return {\n                        provider,\n                        signer\n                    };\n                } else {\n                    throw new Error('MetaMask not found');\n                }\n            } catch (error) {\n                console.error('Error initializing provider:', error);\n                setError('Failed to connect to wallet');\n                return null;\n            }\n        }\n    }[\"useModularToken.useCallback[initializeProvider]\"], []);\n    // Load token information\n    const loadTokenInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadTokenInfo]\": async ()=>{\n            if (!provider || !SECURITY_TOKEN_CORE_ADDRESS) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__, provider);\n                const [name, symbol, version, totalSupply, maxSupply, decimals, paused, metadata] = await Promise.all([\n                    contract.name(),\n                    contract.symbol(),\n                    contract.version(),\n                    contract.totalSupply(),\n                    contract.maxSupply(),\n                    contract.decimals(),\n                    contract.paused(),\n                    contract.getTokenMetadata()\n                ]);\n                setTokenInfo({\n                    name,\n                    symbol,\n                    version,\n                    totalSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(totalSupply, decimals),\n                    maxSupply: ethers__WEBPACK_IMPORTED_MODULE_5__.formatUnits(maxSupply, decimals),\n                    decimals,\n                    paused,\n                    metadata: {\n                        tokenPrice: metadata[0],\n                        bonusTiers: metadata[1],\n                        tokenDetails: metadata[2],\n                        tokenImageUrl: metadata[3]\n                    }\n                });\n            } catch (error) {\n                console.error('Error loading token info:', error);\n                setError('Failed to load token information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadTokenInfo]\"], [\n        provider\n    ]);\n    // Load upgrade information\n    const loadUpgradeInfo = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[loadUpgradeInfo]\": async ()=>{\n            if (!provider || !UPGRADE_MANAGER_ADDRESS) return;\n            try {\n                setLoading(true);\n                const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, provider);\n                const [emergencyModeActive, registeredModules, upgradeDelay, emergencyModeDuration, pendingUpgradeIds] = await Promise.all([\n                    contract.isEmergencyModeActive(),\n                    contract.getRegisteredModules(),\n                    contract.UPGRADE_DELAY(),\n                    contract.EMERGENCY_MODE_DURATION(),\n                    contract.getPendingUpgradeIds()\n                ]);\n                setUpgradeInfo({\n                    emergencyModeActive,\n                    registeredModules,\n                    upgradeDelay: Number(upgradeDelay),\n                    emergencyModeDuration: Number(emergencyModeDuration)\n                });\n                // Load pending upgrades\n                const pendingUpgradesData = await Promise.all(pendingUpgradeIds.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": async (id)=>{\n                        const upgrade = await contract.pendingUpgrades(id);\n                        return {\n                            upgradeId: id,\n                            moduleId: upgrade.moduleId,\n                            proxy: upgrade.proxy,\n                            newImplementation: upgrade.newImplementation,\n                            executeTime: Number(upgrade.executeTime),\n                            executed: upgrade.executed,\n                            cancelled: upgrade.cancelled,\n                            description: upgrade.description\n                        };\n                    }\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n                setPendingUpgrades(pendingUpgradesData);\n                // Load upgrade history for SecurityTokenCore\n                const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n                const history = await contract.getUpgradeHistory(SECURITY_TOKEN_CORE_ID);\n                setUpgradeHistory(history.map({\n                    \"useModularToken.useCallback[loadUpgradeInfo]\": (record)=>({\n                            oldImplementation: record.oldImplementation,\n                            newImplementation: record.newImplementation,\n                            timestamp: Number(record.timestamp),\n                            executor: record.executor,\n                            version: record.version,\n                            isEmergency: record.isEmergency,\n                            description: record.description\n                        })\n                }[\"useModularToken.useCallback[loadUpgradeInfo]\"]));\n            } catch (error) {\n                console.error('Error loading upgrade info:', error);\n                setError('Failed to load upgrade information');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"useModularToken.useCallback[loadUpgradeInfo]\"], [\n        provider\n    ]);\n    // Mint tokens\n    const mintTokens = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[mintTokens]\": async (address, amount)=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__, signer);\n            const decimals = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0;\n            const amountWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(amount, decimals);\n            const tx = await contract.mint(address, amountWei);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[mintTokens]\"], [\n        signer,\n        tokenInfo\n    ]);\n    // Toggle pause state\n    const togglePause = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[togglePause]\": async ()=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__, signer);\n            try {\n                // Add explicit gas limit to avoid estimation issues\n                const gasLimit = 500000;\n                const tx = (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? await contract.unpause({\n                    gasLimit\n                }) : await contract.pause({\n                    gasLimit\n                });\n                return tx.wait();\n            } catch (error) {\n                var _error_error;\n                // Provide more detailed error information\n                console.error('Pause/Unpause error details:', error);\n                if (error.code === 'UNKNOWN_ERROR' && ((_error_error = error.error) === null || _error_error === void 0 ? void 0 : _error_error.code) === -32603) {\n                    throw new Error('Network error: Please check your connection and try again. The Amoy testnet may be experiencing issues.');\n                } else if (error.code === 'ACTION_REJECTED') {\n                    throw new Error('Transaction was rejected by user');\n                } else if (error.reason) {\n                    throw new Error(\"Contract error: \".concat(error.reason));\n                } else {\n                    throw new Error(\"Failed to \".concat((tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.paused) ? 'unpause' : 'pause', \" token: \").concat(error.message));\n                }\n            }\n        }\n    }[\"useModularToken.useCallback[togglePause]\"], [\n        signer,\n        tokenInfo\n    ]);\n    // Schedule upgrade\n    const scheduleUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[scheduleUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.scheduleUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[scheduleUpgrade]\"], [\n        signer\n    ]);\n    // Execute upgrade\n    const executeUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[executeUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, signer);\n            const tx = await contract.executeUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[executeUpgrade]\"], [\n        signer\n    ]);\n    // Emergency upgrade\n    const emergencyUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[emergencyUpgrade]\": async (implementationAddress, description)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, signer);\n            const SECURITY_TOKEN_CORE_ID = ethers__WEBPACK_IMPORTED_MODULE_6__.keccak256(ethers__WEBPACK_IMPORTED_MODULE_7__.toUtf8Bytes(\"SECURITY_TOKEN_CORE\"));\n            const tx = await contract.emergencyUpgrade(SECURITY_TOKEN_CORE_ID, implementationAddress, description);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[emergencyUpgrade]\"], [\n        signer\n    ]);\n    // Toggle emergency mode\n    const toggleEmergencyMode = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[toggleEmergencyMode]\": async ()=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, signer);\n            const tx = (upgradeInfo === null || upgradeInfo === void 0 ? void 0 : upgradeInfo.emergencyModeActive) ? await contract.deactivateEmergencyMode() : await contract.activateEmergencyMode();\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[toggleEmergencyMode]\"], [\n        signer,\n        upgradeInfo\n    ]);\n    // Cancel upgrade\n    const cancelUpgrade = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[cancelUpgrade]\": async (upgradeId)=>{\n            if (!signer || !UPGRADE_MANAGER_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(UPGRADE_MANAGER_ADDRESS, _contracts_UpgradeManager_json__WEBPACK_IMPORTED_MODULE_2__, signer);\n            const tx = await contract.cancelUpgrade(upgradeId);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[cancelUpgrade]\"], [\n        signer\n    ]);\n    // Update token price\n    const updateTokenPrice = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateTokenPrice]\": async (newPrice)=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__, signer);\n            const tx = await contract.updateTokenPrice(newPrice);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[updateTokenPrice]\"], [\n        signer\n    ]);\n    // Update bonus tiers\n    const updateBonusTiers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateBonusTiers]\": async (newBonusTiers)=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__, signer);\n            const tx = await contract.updateBonusTiers(newBonusTiers);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[updateBonusTiers]\"], [\n        signer\n    ]);\n    // Update max supply\n    const updateMaxSupply = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[updateMaxSupply]\": async (newMaxSupply)=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__, signer);\n            const maxSupplyWei = ethers__WEBPACK_IMPORTED_MODULE_5__.parseUnits(newMaxSupply, (tokenInfo === null || tokenInfo === void 0 ? void 0 : tokenInfo.decimals) || 0);\n            const tx = await contract.updateMaxSupply(maxSupplyWei);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[updateMaxSupply]\"], [\n        signer,\n        tokenInfo\n    ]);\n    // Add to whitelist\n    const addToWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[addToWhitelist]\": async (address)=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__, signer);\n            const tx = await contract.addToWhitelist(address);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[addToWhitelist]\"], [\n        signer\n    ]);\n    // Remove from whitelist\n    const removeFromWhitelist = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[removeFromWhitelist]\": async (address)=>{\n            if (!signer || !SECURITY_TOKEN_CORE_ADDRESS) {\n                throw new Error('Wallet not connected');\n            }\n            const contract = new ethers__WEBPACK_IMPORTED_MODULE_4__.Contract(SECURITY_TOKEN_CORE_ADDRESS, _contracts_SecurityTokenCore_json__WEBPACK_IMPORTED_MODULE_1__, signer);\n            const tx = await contract.removeFromWhitelist(address);\n            return tx.wait();\n        }\n    }[\"useModularToken.useCallback[removeFromWhitelist]\"], [\n        signer\n    ]);\n    // Refresh all data\n    const refreshData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useModularToken.useCallback[refreshData]\": async ()=>{\n            await Promise.all([\n                loadTokenInfo(),\n                loadUpgradeInfo()\n            ]);\n        }\n    }[\"useModularToken.useCallback[refreshData]\"], [\n        loadTokenInfo,\n        loadUpgradeInfo\n    ]);\n    // Initialize on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            initializeProvider();\n        }\n    }[\"useModularToken.useEffect\"], [\n        initializeProvider\n    ]);\n    // Load data when provider is ready\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useModularToken.useEffect\": ()=>{\n            if (provider && signer) {\n                refreshData();\n            }\n        }\n    }[\"useModularToken.useEffect\"], [\n        provider,\n        signer,\n        refreshData\n    ]);\n    return {\n        // State\n        provider,\n        signer,\n        tokenInfo,\n        upgradeInfo,\n        pendingUpgrades,\n        upgradeHistory,\n        loading,\n        error,\n        // Actions\n        initializeProvider,\n        loadTokenInfo,\n        loadUpgradeInfo,\n        mintTokens,\n        togglePause,\n        scheduleUpgrade,\n        executeUpgrade,\n        emergencyUpgrade,\n        toggleEmergencyMode,\n        cancelUpgrade,\n        refreshData,\n        // Admin Functions\n        updateTokenPrice,\n        updateBonusTiers,\n        updateMaxSupply,\n        addToWhitelist,\n        removeFromWhitelist,\n        // Utilities\n        setError,\n        setLoading\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VNb2R1bGFyVG9rZW4udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztxRUFFeUQ7QUFDekI7QUFFaEMsY0FBYztBQUN3RDtBQUNOO0FBRWhFLHNDQUFzQztBQUN0QyxNQUFNTSw4QkFBOEJDLDRDQUF3RDtBQUM1RixNQUFNRywwQkFBMEJILDRDQUFvRDtBQThDN0UsU0FBU0s7SUFDZCxNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR2QsK0NBQVFBLENBQWdDO0lBQ3hFLE1BQU0sQ0FBQ2UsUUFBUUMsVUFBVSxHQUFHaEIsK0NBQVFBLENBQThCO0lBQ2xFLE1BQU0sQ0FBQ2lCLFdBQVdDLGFBQWEsR0FBR2xCLCtDQUFRQSxDQUFtQjtJQUM3RCxNQUFNLENBQUNtQixhQUFhQyxlQUFlLEdBQUdwQiwrQ0FBUUEsQ0FBcUI7SUFDbkUsTUFBTSxDQUFDcUIsaUJBQWlCQyxtQkFBbUIsR0FBR3RCLCtDQUFRQSxDQUFtQixFQUFFO0lBQzNFLE1BQU0sQ0FBQ3VCLGdCQUFnQkMsa0JBQWtCLEdBQUd4QiwrQ0FBUUEsQ0FBa0IsRUFBRTtJQUN4RSxNQUFNLENBQUN5QixTQUFTQyxXQUFXLEdBQUcxQiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUMyQixPQUFPQyxTQUFTLEdBQUc1QiwrQ0FBUUEsQ0FBZ0I7SUFFbEQsaUNBQWlDO0lBQ2pDLE1BQU02QixxQkFBcUIzQixrREFBV0E7MkRBQUM7WUFDckMsSUFBSTtnQkFDRixJQUFJLEtBQTZCLElBQUk0QixPQUFPQyxRQUFRLEVBQUU7b0JBQ3BELE1BQU1sQixXQUFXLElBQUlWLG1EQUFzQixDQUFDMkIsT0FBT0MsUUFBUTtvQkFDM0QsTUFBTWxCLFNBQVNvQixJQUFJLENBQUMsdUJBQXVCLEVBQUU7b0JBQzdDLE1BQU1sQixTQUFTLE1BQU1GLFNBQVNxQixTQUFTO29CQUV2Q3BCLFlBQVlEO29CQUNaRyxVQUFVRDtvQkFDVixPQUFPO3dCQUFFRjt3QkFBVUU7b0JBQU87Z0JBQzVCLE9BQU87b0JBQ0wsTUFBTSxJQUFJb0IsTUFBTTtnQkFDbEI7WUFDRixFQUFFLE9BQU9SLE9BQU87Z0JBQ2RTLFFBQVFULEtBQUssQ0FBQyxnQ0FBZ0NBO2dCQUM5Q0MsU0FBUztnQkFDVCxPQUFPO1lBQ1Q7UUFDRjswREFBRyxFQUFFO0lBRUwseUJBQXlCO0lBQ3pCLE1BQU1TLGdCQUFnQm5DLGtEQUFXQTtzREFBQztZQUNoQyxJQUFJLENBQUNXLFlBQVksQ0FBQ1AsNkJBQTZCO1lBRS9DLElBQUk7Z0JBQ0ZvQixXQUFXO2dCQUNYLE1BQU1ZLFdBQVcsSUFBSW5DLDRDQUFlLENBQUNHLDZCQUE2QkYsOERBQW9CQSxFQUFFUztnQkFFeEYsTUFBTSxDQUFDMkIsTUFBTUMsUUFBUUMsU0FBU0MsYUFBYUMsV0FBV0MsVUFBVUMsUUFBUUMsU0FBUyxHQUFHLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQztvQkFDcEdYLFNBQVNFLElBQUk7b0JBQ2JGLFNBQVNHLE1BQU07b0JBQ2ZILFNBQVNJLE9BQU87b0JBQ2hCSixTQUFTSyxXQUFXO29CQUNwQkwsU0FBU00sU0FBUztvQkFDbEJOLFNBQVNPLFFBQVE7b0JBQ2pCUCxTQUFTUSxNQUFNO29CQUNmUixTQUFTWSxnQkFBZ0I7aUJBQzFCO2dCQUVEaEMsYUFBYTtvQkFDWHNCO29CQUNBQztvQkFDQUM7b0JBQ0FDLGFBQWF4QywrQ0FBa0IsQ0FBQ3dDLGFBQWFFO29CQUM3Q0QsV0FBV3pDLCtDQUFrQixDQUFDeUMsV0FBV0M7b0JBQ3pDQTtvQkFDQUM7b0JBQ0FDLFVBQVU7d0JBQ1JLLFlBQVlMLFFBQVEsQ0FBQyxFQUFFO3dCQUN2Qk0sWUFBWU4sUUFBUSxDQUFDLEVBQUU7d0JBQ3ZCTyxjQUFjUCxRQUFRLENBQUMsRUFBRTt3QkFDekJRLGVBQWVSLFFBQVEsQ0FBQyxFQUFFO29CQUM1QjtnQkFDRjtZQUNGLEVBQUUsT0FBT3BCLE9BQU87Z0JBQ2RTLFFBQVFULEtBQUssQ0FBQyw2QkFBNkJBO2dCQUMzQ0MsU0FBUztZQUNYLFNBQVU7Z0JBQ1JGLFdBQVc7WUFDYjtRQUNGO3FEQUFHO1FBQUNiO0tBQVM7SUFFYiwyQkFBMkI7SUFDM0IsTUFBTTJDLGtCQUFrQnRELGtEQUFXQTt3REFBQztZQUNsQyxJQUFJLENBQUNXLFlBQVksQ0FBQ0gseUJBQXlCO1lBRTNDLElBQUk7Z0JBQ0ZnQixXQUFXO2dCQUNYLE1BQU1ZLFdBQVcsSUFBSW5DLDRDQUFlLENBQUNPLHlCQUF5QkwsMkRBQWlCQSxFQUFFUTtnQkFFakYsTUFBTSxDQUFDNEMscUJBQXFCQyxtQkFBbUJDLGNBQWNDLHVCQUF1QkMsa0JBQWtCLEdBQUcsTUFBTWIsUUFBUUMsR0FBRyxDQUFDO29CQUN6SFgsU0FBU3dCLHFCQUFxQjtvQkFDOUJ4QixTQUFTeUIsb0JBQW9CO29CQUM3QnpCLFNBQVMwQixhQUFhO29CQUN0QjFCLFNBQVMyQix1QkFBdUI7b0JBQ2hDM0IsU0FBUzRCLG9CQUFvQjtpQkFDOUI7Z0JBRUQ5QyxlQUFlO29CQUNicUM7b0JBQ0FDO29CQUNBQyxjQUFjUSxPQUFPUjtvQkFDckJDLHVCQUF1Qk8sT0FBT1A7Z0JBQ2hDO2dCQUVBLHdCQUF3QjtnQkFDeEIsTUFBTVEsc0JBQXNCLE1BQU1wQixRQUFRQyxHQUFHLENBQzNDWSxrQkFBa0JRLEdBQUc7b0VBQUMsT0FBT0M7d0JBQzNCLE1BQU1DLFVBQVUsTUFBTWpDLFNBQVNqQixlQUFlLENBQUNpRDt3QkFDL0MsT0FBTzs0QkFDTEUsV0FBV0Y7NEJBQ1hHLFVBQVVGLFFBQVFFLFFBQVE7NEJBQzFCQyxPQUFPSCxRQUFRRyxLQUFLOzRCQUNwQkMsbUJBQW1CSixRQUFRSSxpQkFBaUI7NEJBQzVDQyxhQUFhVCxPQUFPSSxRQUFRSyxXQUFXOzRCQUN2Q0MsVUFBVU4sUUFBUU0sUUFBUTs0QkFDMUJDLFdBQVdQLFFBQVFPLFNBQVM7NEJBQzVCQyxhQUFhUixRQUFRUSxXQUFXO3dCQUNsQztvQkFDRjs7Z0JBR0Z6RCxtQkFBbUI4QztnQkFFbkIsNkNBQTZDO2dCQUM3QyxNQUFNWSx5QkFBeUI3RSw2Q0FBZ0IsQ0FBQ0EsK0NBQWtCLENBQUM7Z0JBQ25FLE1BQU1nRixVQUFVLE1BQU03QyxTQUFTOEMsaUJBQWlCLENBQUNKO2dCQUVqRHhELGtCQUFrQjJELFFBQVFkLEdBQUc7b0VBQUMsQ0FBQ2dCLFNBQWlCOzRCQUM5Q0MsbUJBQW1CRCxPQUFPQyxpQkFBaUI7NEJBQzNDWCxtQkFBbUJVLE9BQU9WLGlCQUFpQjs0QkFDM0NZLFdBQVdwQixPQUFPa0IsT0FBT0UsU0FBUzs0QkFDbENDLFVBQVVILE9BQU9HLFFBQVE7NEJBQ3pCOUMsU0FBUzJDLE9BQU8zQyxPQUFPOzRCQUN2QitDLGFBQWFKLE9BQU9JLFdBQVc7NEJBQy9CVixhQUFhTSxPQUFPTixXQUFXO3dCQUNqQzs7WUFFRixFQUFFLE9BQU9wRCxPQUFPO2dCQUNkUyxRQUFRVCxLQUFLLENBQUMsK0JBQStCQTtnQkFDN0NDLFNBQVM7WUFDWCxTQUFVO2dCQUNSRixXQUFXO1lBQ2I7UUFDRjt1REFBRztRQUFDYjtLQUFTO0lBRWIsY0FBYztJQUNkLE1BQU02RSxhQUFheEYsa0RBQVdBO21EQUFDLE9BQU95RixTQUFpQkM7WUFDckQsSUFBSSxDQUFDN0UsVUFBVSxDQUFDVCw2QkFBNkI7Z0JBQzNDLE1BQU0sSUFBSTZCLE1BQU07WUFDbEI7WUFFQSxNQUFNRyxXQUFXLElBQUluQyw0Q0FBZSxDQUFDRyw2QkFBNkJGLDhEQUFvQkEsRUFBRVc7WUFDeEYsTUFBTThCLFdBQVc1QixDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVc0QixRQUFRLEtBQUk7WUFDeEMsTUFBTWdELFlBQVkxRiw4Q0FBaUIsQ0FBQ3lGLFFBQVEvQztZQUU1QyxNQUFNa0QsS0FBSyxNQUFNekQsU0FBUzBELElBQUksQ0FBQ0wsU0FBU0U7WUFDeEMsT0FBT0UsR0FBR0UsSUFBSTtRQUNoQjtrREFBRztRQUFDbEY7UUFBUUU7S0FBVTtJQUV0QixxQkFBcUI7SUFDckIsTUFBTWlGLGNBQWNoRyxrREFBV0E7b0RBQUM7WUFDOUIsSUFBSSxDQUFDYSxVQUFVLENBQUNULDZCQUE2QjtnQkFDM0MsTUFBTSxJQUFJNkIsTUFBTTtZQUNsQjtZQUVBLE1BQU1HLFdBQVcsSUFBSW5DLDRDQUFlLENBQUNHLDZCQUE2QkYsOERBQW9CQSxFQUFFVztZQUV4RixJQUFJO2dCQUNGLG9EQUFvRDtnQkFDcEQsTUFBTW9GLFdBQVc7Z0JBRWpCLE1BQU1KLEtBQUs5RSxDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVc2QixNQUFNLElBQ3hCLE1BQU1SLFNBQVM4RCxPQUFPLENBQUM7b0JBQUVEO2dCQUFTLEtBQ2xDLE1BQU03RCxTQUFTK0QsS0FBSyxDQUFDO29CQUFFRjtnQkFBUztnQkFFcEMsT0FBT0osR0FBR0UsSUFBSTtZQUNoQixFQUFFLE9BQU90RSxPQUFZO29CQUltQkE7Z0JBSHRDLDBDQUEwQztnQkFDMUNTLFFBQVFULEtBQUssQ0FBQyxnQ0FBZ0NBO2dCQUU5QyxJQUFJQSxNQUFNMkUsSUFBSSxLQUFLLG1CQUFtQjNFLEVBQUFBLGVBQUFBLE1BQU1BLEtBQUssY0FBWEEsbUNBQUFBLGFBQWEyRSxJQUFJLE1BQUssQ0FBQyxPQUFPO29CQUNsRSxNQUFNLElBQUluRSxNQUFNO2dCQUNsQixPQUFPLElBQUlSLE1BQU0yRSxJQUFJLEtBQUssbUJBQW1CO29CQUMzQyxNQUFNLElBQUluRSxNQUFNO2dCQUNsQixPQUFPLElBQUlSLE1BQU00RSxNQUFNLEVBQUU7b0JBQ3ZCLE1BQU0sSUFBSXBFLE1BQU0sbUJBQWdDLE9BQWJSLE1BQU00RSxNQUFNO2dCQUNqRCxPQUFPO29CQUNMLE1BQU0sSUFBSXBFLE1BQU0sYUFBK0RSLE9BQWxEVixDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVc2QixNQUFNLElBQUcsWUFBWSxTQUFRLFlBQXdCLE9BQWRuQixNQUFNNkUsT0FBTztnQkFDOUY7WUFDRjtRQUNGO21EQUFHO1FBQUN6RjtRQUFRRTtLQUFVO0lBRXRCLG1CQUFtQjtJQUNuQixNQUFNd0Ysa0JBQWtCdkcsa0RBQVdBO3dEQUFDLE9BQU93Ryx1QkFBK0IzQjtZQUN4RSxJQUFJLENBQUNoRSxVQUFVLENBQUNMLHlCQUF5QjtnQkFDdkMsTUFBTSxJQUFJeUIsTUFBTTtZQUNsQjtZQUVBLE1BQU1HLFdBQVcsSUFBSW5DLDRDQUFlLENBQUNPLHlCQUF5QkwsMkRBQWlCQSxFQUFFVTtZQUNqRixNQUFNaUUseUJBQXlCN0UsNkNBQWdCLENBQUNBLCtDQUFrQixDQUFDO1lBRW5FLE1BQU00RixLQUFLLE1BQU16RCxTQUFTbUUsZUFBZSxDQUN2Q3pCLHdCQUNBMEIsdUJBQ0EzQjtZQUdGLE9BQU9nQixHQUFHRSxJQUFJO1FBQ2hCO3VEQUFHO1FBQUNsRjtLQUFPO0lBRVgsa0JBQWtCO0lBQ2xCLE1BQU00RixpQkFBaUJ6RyxrREFBV0E7dURBQUMsT0FBT3NFO1lBQ3hDLElBQUksQ0FBQ3pELFVBQVUsQ0FBQ0wseUJBQXlCO2dCQUN2QyxNQUFNLElBQUl5QixNQUFNO1lBQ2xCO1lBRUEsTUFBTUcsV0FBVyxJQUFJbkMsNENBQWUsQ0FBQ08seUJBQXlCTCwyREFBaUJBLEVBQUVVO1lBQ2pGLE1BQU1nRixLQUFLLE1BQU16RCxTQUFTcUUsY0FBYyxDQUFDbkM7WUFDekMsT0FBT3VCLEdBQUdFLElBQUk7UUFDaEI7c0RBQUc7UUFBQ2xGO0tBQU87SUFFWCxvQkFBb0I7SUFDcEIsTUFBTTZGLG1CQUFtQjFHLGtEQUFXQTt5REFBQyxPQUFPd0csdUJBQStCM0I7WUFDekUsSUFBSSxDQUFDaEUsVUFBVSxDQUFDTCx5QkFBeUI7Z0JBQ3ZDLE1BQU0sSUFBSXlCLE1BQU07WUFDbEI7WUFFQSxNQUFNRyxXQUFXLElBQUluQyw0Q0FBZSxDQUFDTyx5QkFBeUJMLDJEQUFpQkEsRUFBRVU7WUFDakYsTUFBTWlFLHlCQUF5QjdFLDZDQUFnQixDQUFDQSwrQ0FBa0IsQ0FBQztZQUVuRSxNQUFNNEYsS0FBSyxNQUFNekQsU0FBU3NFLGdCQUFnQixDQUN4QzVCLHdCQUNBMEIsdUJBQ0EzQjtZQUdGLE9BQU9nQixHQUFHRSxJQUFJO1FBQ2hCO3dEQUFHO1FBQUNsRjtLQUFPO0lBRVgsd0JBQXdCO0lBQ3hCLE1BQU04RixzQkFBc0IzRyxrREFBV0E7NERBQUM7WUFDdEMsSUFBSSxDQUFDYSxVQUFVLENBQUNMLHlCQUF5QjtnQkFDdkMsTUFBTSxJQUFJeUIsTUFBTTtZQUNsQjtZQUVBLE1BQU1HLFdBQVcsSUFBSW5DLDRDQUFlLENBQUNPLHlCQUF5QkwsMkRBQWlCQSxFQUFFVTtZQUVqRixNQUFNZ0YsS0FBSzVFLENBQUFBLHdCQUFBQSxrQ0FBQUEsWUFBYXNDLG1CQUFtQixJQUN2QyxNQUFNbkIsU0FBU3dFLHVCQUF1QixLQUN0QyxNQUFNeEUsU0FBU3lFLHFCQUFxQjtZQUV4QyxPQUFPaEIsR0FBR0UsSUFBSTtRQUNoQjsyREFBRztRQUFDbEY7UUFBUUk7S0FBWTtJQUV4QixpQkFBaUI7SUFDakIsTUFBTTZGLGdCQUFnQjlHLGtEQUFXQTtzREFBQyxPQUFPc0U7WUFDdkMsSUFBSSxDQUFDekQsVUFBVSxDQUFDTCx5QkFBeUI7Z0JBQ3ZDLE1BQU0sSUFBSXlCLE1BQU07WUFDbEI7WUFFQSxNQUFNRyxXQUFXLElBQUluQyw0Q0FBZSxDQUFDTyx5QkFBeUJMLDJEQUFpQkEsRUFBRVU7WUFDakYsTUFBTWdGLEtBQUssTUFBTXpELFNBQVMwRSxhQUFhLENBQUN4QztZQUN4QyxPQUFPdUIsR0FBR0UsSUFBSTtRQUNoQjtxREFBRztRQUFDbEY7S0FBTztJQUVYLHFCQUFxQjtJQUNyQixNQUFNa0csbUJBQW1CL0csa0RBQVdBO3lEQUFDLE9BQU9nSDtZQUMxQyxJQUFJLENBQUNuRyxVQUFVLENBQUNULDZCQUE2QjtnQkFDM0MsTUFBTSxJQUFJNkIsTUFBTTtZQUNsQjtZQUVBLE1BQU1HLFdBQVcsSUFBSW5DLDRDQUFlLENBQUNHLDZCQUE2QkYsOERBQW9CQSxFQUFFVztZQUN4RixNQUFNZ0YsS0FBSyxNQUFNekQsU0FBUzJFLGdCQUFnQixDQUFDQztZQUMzQyxPQUFPbkIsR0FBR0UsSUFBSTtRQUNoQjt3REFBRztRQUFDbEY7S0FBTztJQUVYLHFCQUFxQjtJQUNyQixNQUFNb0csbUJBQW1Cakgsa0RBQVdBO3lEQUFDLE9BQU9rSDtZQUMxQyxJQUFJLENBQUNyRyxVQUFVLENBQUNULDZCQUE2QjtnQkFDM0MsTUFBTSxJQUFJNkIsTUFBTTtZQUNsQjtZQUVBLE1BQU1HLFdBQVcsSUFBSW5DLDRDQUFlLENBQUNHLDZCQUE2QkYsOERBQW9CQSxFQUFFVztZQUN4RixNQUFNZ0YsS0FBSyxNQUFNekQsU0FBUzZFLGdCQUFnQixDQUFDQztZQUMzQyxPQUFPckIsR0FBR0UsSUFBSTtRQUNoQjt3REFBRztRQUFDbEY7S0FBTztJQUVYLG9CQUFvQjtJQUNwQixNQUFNc0csa0JBQWtCbkgsa0RBQVdBO3dEQUFDLE9BQU9vSDtZQUN6QyxJQUFJLENBQUN2RyxVQUFVLENBQUNULDZCQUE2QjtnQkFDM0MsTUFBTSxJQUFJNkIsTUFBTTtZQUNsQjtZQUVBLE1BQU1HLFdBQVcsSUFBSW5DLDRDQUFlLENBQUNHLDZCQUE2QkYsOERBQW9CQSxFQUFFVztZQUN4RixNQUFNd0csZUFBZXBILDhDQUFpQixDQUFDbUgsY0FBY3JHLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBVzRCLFFBQVEsS0FBSTtZQUM1RSxNQUFNa0QsS0FBSyxNQUFNekQsU0FBUytFLGVBQWUsQ0FBQ0U7WUFDMUMsT0FBT3hCLEdBQUdFLElBQUk7UUFDaEI7dURBQUc7UUFBQ2xGO1FBQVFFO0tBQVU7SUFFdEIsbUJBQW1CO0lBQ25CLE1BQU11RyxpQkFBaUJ0SCxrREFBV0E7dURBQUMsT0FBT3lGO1lBQ3hDLElBQUksQ0FBQzVFLFVBQVUsQ0FBQ1QsNkJBQTZCO2dCQUMzQyxNQUFNLElBQUk2QixNQUFNO1lBQ2xCO1lBRUEsTUFBTUcsV0FBVyxJQUFJbkMsNENBQWUsQ0FBQ0csNkJBQTZCRiw4REFBb0JBLEVBQUVXO1lBQ3hGLE1BQU1nRixLQUFLLE1BQU16RCxTQUFTa0YsY0FBYyxDQUFDN0I7WUFDekMsT0FBT0ksR0FBR0UsSUFBSTtRQUNoQjtzREFBRztRQUFDbEY7S0FBTztJQUVYLHdCQUF3QjtJQUN4QixNQUFNMEcsc0JBQXNCdkgsa0RBQVdBOzREQUFDLE9BQU95RjtZQUM3QyxJQUFJLENBQUM1RSxVQUFVLENBQUNULDZCQUE2QjtnQkFDM0MsTUFBTSxJQUFJNkIsTUFBTTtZQUNsQjtZQUVBLE1BQU1HLFdBQVcsSUFBSW5DLDRDQUFlLENBQUNHLDZCQUE2QkYsOERBQW9CQSxFQUFFVztZQUN4RixNQUFNZ0YsS0FBSyxNQUFNekQsU0FBU21GLG1CQUFtQixDQUFDOUI7WUFDOUMsT0FBT0ksR0FBR0UsSUFBSTtRQUNoQjsyREFBRztRQUFDbEY7S0FBTztJQUVYLG1CQUFtQjtJQUNuQixNQUFNMkcsY0FBY3hILGtEQUFXQTtvREFBQztZQUM5QixNQUFNOEMsUUFBUUMsR0FBRyxDQUFDO2dCQUNoQlo7Z0JBQ0FtQjthQUNEO1FBQ0g7bURBQUc7UUFBQ25CO1FBQWVtQjtLQUFnQjtJQUVuQyxzQkFBc0I7SUFDdEJ2RCxnREFBU0E7cUNBQUM7WUFDUjRCO1FBQ0Y7b0NBQUc7UUFBQ0E7S0FBbUI7SUFFdkIsbUNBQW1DO0lBQ25DNUIsZ0RBQVNBO3FDQUFDO1lBQ1IsSUFBSVksWUFBWUUsUUFBUTtnQkFDdEIyRztZQUNGO1FBQ0Y7b0NBQUc7UUFBQzdHO1FBQVVFO1FBQVEyRztLQUFZO0lBRWxDLE9BQU87UUFDTCxRQUFRO1FBQ1I3RztRQUNBRTtRQUNBRTtRQUNBRTtRQUNBRTtRQUNBRTtRQUNBRTtRQUNBRTtRQUVBLFVBQVU7UUFDVkU7UUFDQVE7UUFDQW1CO1FBQ0FrQztRQUNBUTtRQUNBTztRQUNBRTtRQUNBQztRQUNBQztRQUNBRztRQUNBVTtRQUVBLGtCQUFrQjtRQUNsQlQ7UUFDQUU7UUFDQUU7UUFDQUc7UUFDQUM7UUFFQSxZQUFZO1FBQ1o3RjtRQUNBRjtJQUNGO0FBQ0YiLCJzb3VyY2VzIjpbIkQ6XFxnaXRodWJcXHRva2VuZGV2LW5ld3Jvb1xcYWRtaW4tcGFuZWxcXHNyY1xcaG9va3NcXHVzZU1vZHVsYXJUb2tlbi50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgZXRoZXJzIH0gZnJvbSAnZXRoZXJzJztcblxuLy8gSW1wb3J0IEFCSXNcbmltcG9ydCBTZWN1cml0eVRva2VuQ29yZUFCSSBmcm9tICdAL2NvbnRyYWN0cy9TZWN1cml0eVRva2VuQ29yZS5qc29uJztcbmltcG9ydCBVcGdyYWRlTWFuYWdlckFCSSBmcm9tICdAL2NvbnRyYWN0cy9VcGdyYWRlTWFuYWdlci5qc29uJztcblxuLy8gQ29udHJhY3QgYWRkcmVzc2VzIGZyb20gZW52aXJvbm1lbnRcbmNvbnN0IFNFQ1VSSVRZX1RPS0VOX0NPUkVfQUREUkVTUyA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FNT1lfU0VDVVJJVFlfVE9LRU5fQ09SRV9BRERSRVNTO1xuY29uc3QgVVBHUkFERV9NQU5BR0VSX0FERFJFU1MgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BTU9ZX1VQR1JBREVfTUFOQUdFUl9BRERSRVNTO1xuXG5pbnRlcmZhY2UgVG9rZW5JbmZvIHtcbiAgbmFtZTogc3RyaW5nO1xuICBzeW1ib2w6IHN0cmluZztcbiAgdmVyc2lvbjogc3RyaW5nO1xuICB0b3RhbFN1cHBseTogc3RyaW5nO1xuICBtYXhTdXBwbHk6IHN0cmluZztcbiAgZGVjaW1hbHM6IG51bWJlcjtcbiAgcGF1c2VkOiBib29sZWFuO1xuICBtZXRhZGF0YToge1xuICAgIHRva2VuUHJpY2U6IHN0cmluZztcbiAgICBib251c1RpZXJzOiBzdHJpbmc7XG4gICAgdG9rZW5EZXRhaWxzOiBzdHJpbmc7XG4gICAgdG9rZW5JbWFnZVVybDogc3RyaW5nO1xuICB9O1xufVxuXG5pbnRlcmZhY2UgVXBncmFkZUluZm8ge1xuICBlbWVyZ2VuY3lNb2RlQWN0aXZlOiBib29sZWFuO1xuICByZWdpc3RlcmVkTW9kdWxlczogc3RyaW5nW107XG4gIHVwZ3JhZGVEZWxheTogbnVtYmVyO1xuICBlbWVyZ2VuY3lNb2RlRHVyYXRpb246IG51bWJlcjtcbn1cblxuaW50ZXJmYWNlIFBlbmRpbmdVcGdyYWRlIHtcbiAgdXBncmFkZUlkOiBzdHJpbmc7XG4gIG1vZHVsZUlkOiBzdHJpbmc7XG4gIHByb3h5OiBzdHJpbmc7XG4gIG5ld0ltcGxlbWVudGF0aW9uOiBzdHJpbmc7XG4gIGV4ZWN1dGVUaW1lOiBudW1iZXI7XG4gIGV4ZWN1dGVkOiBib29sZWFuO1xuICBjYW5jZWxsZWQ6IGJvb2xlYW47XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBVcGdyYWRlUmVjb3JkIHtcbiAgb2xkSW1wbGVtZW50YXRpb246IHN0cmluZztcbiAgbmV3SW1wbGVtZW50YXRpb246IHN0cmluZztcbiAgdGltZXN0YW1wOiBudW1iZXI7XG4gIGV4ZWN1dG9yOiBzdHJpbmc7XG4gIHZlcnNpb246IHN0cmluZztcbiAgaXNFbWVyZ2VuY3k6IGJvb2xlYW47XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VNb2R1bGFyVG9rZW4oKSB7XG4gIGNvbnN0IFtwcm92aWRlciwgc2V0UHJvdmlkZXJdID0gdXNlU3RhdGU8ZXRoZXJzLkJyb3dzZXJQcm92aWRlciB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbc2lnbmVyLCBzZXRTaWduZXJdID0gdXNlU3RhdGU8ZXRoZXJzLkpzb25ScGNTaWduZXIgfCBudWxsPihudWxsKTtcbiAgY29uc3QgW3Rva2VuSW5mbywgc2V0VG9rZW5JbmZvXSA9IHVzZVN0YXRlPFRva2VuSW5mbyB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbdXBncmFkZUluZm8sIHNldFVwZ3JhZGVJbmZvXSA9IHVzZVN0YXRlPFVwZ3JhZGVJbmZvIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtwZW5kaW5nVXBncmFkZXMsIHNldFBlbmRpbmdVcGdyYWRlc10gPSB1c2VTdGF0ZTxQZW5kaW5nVXBncmFkZVtdPihbXSk7XG4gIGNvbnN0IFt1cGdyYWRlSGlzdG9yeSwgc2V0VXBncmFkZUhpc3RvcnldID0gdXNlU3RhdGU8VXBncmFkZVJlY29yZFtdPihbXSk7XG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcblxuICAvLyBJbml0aWFsaXplIHByb3ZpZGVyIGFuZCBzaWduZXJcbiAgY29uc3QgaW5pdGlhbGl6ZVByb3ZpZGVyID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgd2luZG93LmV0aGVyZXVtKSB7XG4gICAgICAgIGNvbnN0IHByb3ZpZGVyID0gbmV3IGV0aGVycy5Ccm93c2VyUHJvdmlkZXIod2luZG93LmV0aGVyZXVtKTtcbiAgICAgICAgYXdhaXQgcHJvdmlkZXIuc2VuZCgnZXRoX3JlcXVlc3RBY2NvdW50cycsIFtdKTtcbiAgICAgICAgY29uc3Qgc2lnbmVyID0gYXdhaXQgcHJvdmlkZXIuZ2V0U2lnbmVyKCk7XG4gICAgICAgIFxuICAgICAgICBzZXRQcm92aWRlcihwcm92aWRlcik7XG4gICAgICAgIHNldFNpZ25lcihzaWduZXIpO1xuICAgICAgICByZXR1cm4geyBwcm92aWRlciwgc2lnbmVyIH07XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ01ldGFNYXNrIG5vdCBmb3VuZCcpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBpbml0aWFsaXppbmcgcHJvdmlkZXI6JywgZXJyb3IpO1xuICAgICAgc2V0RXJyb3IoJ0ZhaWxlZCB0byBjb25uZWN0IHRvIHdhbGxldCcpO1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgLy8gTG9hZCB0b2tlbiBpbmZvcm1hdGlvblxuICBjb25zdCBsb2FkVG9rZW5JbmZvID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIGlmICghcHJvdmlkZXIgfHwgIVNFQ1VSSVRZX1RPS0VOX0NPUkVfQUREUkVTUykgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIHNldExvYWRpbmcodHJ1ZSk7XG4gICAgICBjb25zdCBjb250cmFjdCA9IG5ldyBldGhlcnMuQ29udHJhY3QoU0VDVVJJVFlfVE9LRU5fQ09SRV9BRERSRVNTLCBTZWN1cml0eVRva2VuQ29yZUFCSSwgcHJvdmlkZXIpO1xuICAgICAgXG4gICAgICBjb25zdCBbbmFtZSwgc3ltYm9sLCB2ZXJzaW9uLCB0b3RhbFN1cHBseSwgbWF4U3VwcGx5LCBkZWNpbWFscywgcGF1c2VkLCBtZXRhZGF0YV0gPSBhd2FpdCBQcm9taXNlLmFsbChbXG4gICAgICAgIGNvbnRyYWN0Lm5hbWUoKSxcbiAgICAgICAgY29udHJhY3Quc3ltYm9sKCksXG4gICAgICAgIGNvbnRyYWN0LnZlcnNpb24oKSxcbiAgICAgICAgY29udHJhY3QudG90YWxTdXBwbHkoKSxcbiAgICAgICAgY29udHJhY3QubWF4U3VwcGx5KCksXG4gICAgICAgIGNvbnRyYWN0LmRlY2ltYWxzKCksXG4gICAgICAgIGNvbnRyYWN0LnBhdXNlZCgpLFxuICAgICAgICBjb250cmFjdC5nZXRUb2tlbk1ldGFkYXRhKClcbiAgICAgIF0pO1xuXG4gICAgICBzZXRUb2tlbkluZm8oe1xuICAgICAgICBuYW1lLFxuICAgICAgICBzeW1ib2wsXG4gICAgICAgIHZlcnNpb24sXG4gICAgICAgIHRvdGFsU3VwcGx5OiBldGhlcnMuZm9ybWF0VW5pdHModG90YWxTdXBwbHksIGRlY2ltYWxzKSxcbiAgICAgICAgbWF4U3VwcGx5OiBldGhlcnMuZm9ybWF0VW5pdHMobWF4U3VwcGx5LCBkZWNpbWFscyksXG4gICAgICAgIGRlY2ltYWxzLFxuICAgICAgICBwYXVzZWQsXG4gICAgICAgIG1ldGFkYXRhOiB7XG4gICAgICAgICAgdG9rZW5QcmljZTogbWV0YWRhdGFbMF0sXG4gICAgICAgICAgYm9udXNUaWVyczogbWV0YWRhdGFbMV0sXG4gICAgICAgICAgdG9rZW5EZXRhaWxzOiBtZXRhZGF0YVsyXSxcbiAgICAgICAgICB0b2tlbkltYWdlVXJsOiBtZXRhZGF0YVszXVxuICAgICAgICB9XG4gICAgICB9KTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyB0b2tlbiBpbmZvOicsIGVycm9yKTtcbiAgICAgIHNldEVycm9yKCdGYWlsZWQgdG8gbG9hZCB0b2tlbiBpbmZvcm1hdGlvbicpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH0sIFtwcm92aWRlcl0pO1xuXG4gIC8vIExvYWQgdXBncmFkZSBpbmZvcm1hdGlvblxuICBjb25zdCBsb2FkVXBncmFkZUluZm8gPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFwcm92aWRlciB8fCAhVVBHUkFERV9NQU5BR0VSX0FERFJFU1MpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgICAgY29uc3QgY29udHJhY3QgPSBuZXcgZXRoZXJzLkNvbnRyYWN0KFVQR1JBREVfTUFOQUdFUl9BRERSRVNTLCBVcGdyYWRlTWFuYWdlckFCSSwgcHJvdmlkZXIpO1xuICAgICAgXG4gICAgICBjb25zdCBbZW1lcmdlbmN5TW9kZUFjdGl2ZSwgcmVnaXN0ZXJlZE1vZHVsZXMsIHVwZ3JhZGVEZWxheSwgZW1lcmdlbmN5TW9kZUR1cmF0aW9uLCBwZW5kaW5nVXBncmFkZUlkc10gPSBhd2FpdCBQcm9taXNlLmFsbChbXG4gICAgICAgIGNvbnRyYWN0LmlzRW1lcmdlbmN5TW9kZUFjdGl2ZSgpLFxuICAgICAgICBjb250cmFjdC5nZXRSZWdpc3RlcmVkTW9kdWxlcygpLFxuICAgICAgICBjb250cmFjdC5VUEdSQURFX0RFTEFZKCksXG4gICAgICAgIGNvbnRyYWN0LkVNRVJHRU5DWV9NT0RFX0RVUkFUSU9OKCksXG4gICAgICAgIGNvbnRyYWN0LmdldFBlbmRpbmdVcGdyYWRlSWRzKClcbiAgICAgIF0pO1xuXG4gICAgICBzZXRVcGdyYWRlSW5mbyh7XG4gICAgICAgIGVtZXJnZW5jeU1vZGVBY3RpdmUsXG4gICAgICAgIHJlZ2lzdGVyZWRNb2R1bGVzLFxuICAgICAgICB1cGdyYWRlRGVsYXk6IE51bWJlcih1cGdyYWRlRGVsYXkpLFxuICAgICAgICBlbWVyZ2VuY3lNb2RlRHVyYXRpb246IE51bWJlcihlbWVyZ2VuY3lNb2RlRHVyYXRpb24pXG4gICAgICB9KTtcblxuICAgICAgLy8gTG9hZCBwZW5kaW5nIHVwZ3JhZGVzXG4gICAgICBjb25zdCBwZW5kaW5nVXBncmFkZXNEYXRhID0gYXdhaXQgUHJvbWlzZS5hbGwoXG4gICAgICAgIHBlbmRpbmdVcGdyYWRlSWRzLm1hcChhc3luYyAoaWQ6IHN0cmluZykgPT4ge1xuICAgICAgICAgIGNvbnN0IHVwZ3JhZGUgPSBhd2FpdCBjb250cmFjdC5wZW5kaW5nVXBncmFkZXMoaWQpO1xuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICB1cGdyYWRlSWQ6IGlkLFxuICAgICAgICAgICAgbW9kdWxlSWQ6IHVwZ3JhZGUubW9kdWxlSWQsXG4gICAgICAgICAgICBwcm94eTogdXBncmFkZS5wcm94eSxcbiAgICAgICAgICAgIG5ld0ltcGxlbWVudGF0aW9uOiB1cGdyYWRlLm5ld0ltcGxlbWVudGF0aW9uLFxuICAgICAgICAgICAgZXhlY3V0ZVRpbWU6IE51bWJlcih1cGdyYWRlLmV4ZWN1dGVUaW1lKSxcbiAgICAgICAgICAgIGV4ZWN1dGVkOiB1cGdyYWRlLmV4ZWN1dGVkLFxuICAgICAgICAgICAgY2FuY2VsbGVkOiB1cGdyYWRlLmNhbmNlbGxlZCxcbiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiB1cGdyYWRlLmRlc2NyaXB0aW9uXG4gICAgICAgICAgfTtcbiAgICAgICAgfSlcbiAgICAgICk7XG5cbiAgICAgIHNldFBlbmRpbmdVcGdyYWRlcyhwZW5kaW5nVXBncmFkZXNEYXRhKTtcblxuICAgICAgLy8gTG9hZCB1cGdyYWRlIGhpc3RvcnkgZm9yIFNlY3VyaXR5VG9rZW5Db3JlXG4gICAgICBjb25zdCBTRUNVUklUWV9UT0tFTl9DT1JFX0lEID0gZXRoZXJzLmtlY2NhazI1NihldGhlcnMudG9VdGY4Qnl0ZXMoXCJTRUNVUklUWV9UT0tFTl9DT1JFXCIpKTtcbiAgICAgIGNvbnN0IGhpc3RvcnkgPSBhd2FpdCBjb250cmFjdC5nZXRVcGdyYWRlSGlzdG9yeShTRUNVUklUWV9UT0tFTl9DT1JFX0lEKTtcbiAgICAgIFxuICAgICAgc2V0VXBncmFkZUhpc3RvcnkoaGlzdG9yeS5tYXAoKHJlY29yZDogYW55KSA9PiAoe1xuICAgICAgICBvbGRJbXBsZW1lbnRhdGlvbjogcmVjb3JkLm9sZEltcGxlbWVudGF0aW9uLFxuICAgICAgICBuZXdJbXBsZW1lbnRhdGlvbjogcmVjb3JkLm5ld0ltcGxlbWVudGF0aW9uLFxuICAgICAgICB0aW1lc3RhbXA6IE51bWJlcihyZWNvcmQudGltZXN0YW1wKSxcbiAgICAgICAgZXhlY3V0b3I6IHJlY29yZC5leGVjdXRvcixcbiAgICAgICAgdmVyc2lvbjogcmVjb3JkLnZlcnNpb24sXG4gICAgICAgIGlzRW1lcmdlbmN5OiByZWNvcmQuaXNFbWVyZ2VuY3ksXG4gICAgICAgIGRlc2NyaXB0aW9uOiByZWNvcmQuZGVzY3JpcHRpb25cbiAgICAgIH0pKSk7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyB1cGdyYWRlIGluZm86JywgZXJyb3IpO1xuICAgICAgc2V0RXJyb3IoJ0ZhaWxlZCB0byBsb2FkIHVwZ3JhZGUgaW5mb3JtYXRpb24nKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9LCBbcHJvdmlkZXJdKTtcblxuICAvLyBNaW50IHRva2Vuc1xuICBjb25zdCBtaW50VG9rZW5zID0gdXNlQ2FsbGJhY2soYXN5bmMgKGFkZHJlc3M6IHN0cmluZywgYW1vdW50OiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIXNpZ25lciB8fCAhU0VDVVJJVFlfVE9LRU5fQ09SRV9BRERSRVNTKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1dhbGxldCBub3QgY29ubmVjdGVkJyk7XG4gICAgfVxuXG4gICAgY29uc3QgY29udHJhY3QgPSBuZXcgZXRoZXJzLkNvbnRyYWN0KFNFQ1VSSVRZX1RPS0VOX0NPUkVfQUREUkVTUywgU2VjdXJpdHlUb2tlbkNvcmVBQkksIHNpZ25lcik7XG4gICAgY29uc3QgZGVjaW1hbHMgPSB0b2tlbkluZm8/LmRlY2ltYWxzIHx8IDA7XG4gICAgY29uc3QgYW1vdW50V2VpID0gZXRoZXJzLnBhcnNlVW5pdHMoYW1vdW50LCBkZWNpbWFscyk7XG4gICAgXG4gICAgY29uc3QgdHggPSBhd2FpdCBjb250cmFjdC5taW50KGFkZHJlc3MsIGFtb3VudFdlaSk7XG4gICAgcmV0dXJuIHR4LndhaXQoKTtcbiAgfSwgW3NpZ25lciwgdG9rZW5JbmZvXSk7XG5cbiAgLy8gVG9nZ2xlIHBhdXNlIHN0YXRlXG4gIGNvbnN0IHRvZ2dsZVBhdXNlID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIGlmICghc2lnbmVyIHx8ICFTRUNVUklUWV9UT0tFTl9DT1JFX0FERFJFU1MpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignV2FsbGV0IG5vdCBjb25uZWN0ZWQnKTtcbiAgICB9XG5cbiAgICBjb25zdCBjb250cmFjdCA9IG5ldyBldGhlcnMuQ29udHJhY3QoU0VDVVJJVFlfVE9LRU5fQ09SRV9BRERSRVNTLCBTZWN1cml0eVRva2VuQ29yZUFCSSwgc2lnbmVyKTtcblxuICAgIHRyeSB7XG4gICAgICAvLyBBZGQgZXhwbGljaXQgZ2FzIGxpbWl0IHRvIGF2b2lkIGVzdGltYXRpb24gaXNzdWVzXG4gICAgICBjb25zdCBnYXNMaW1pdCA9IDUwMDAwMDtcblxuICAgICAgY29uc3QgdHggPSB0b2tlbkluZm8/LnBhdXNlZFxuICAgICAgICA/IGF3YWl0IGNvbnRyYWN0LnVucGF1c2UoeyBnYXNMaW1pdCB9KVxuICAgICAgICA6IGF3YWl0IGNvbnRyYWN0LnBhdXNlKHsgZ2FzTGltaXQgfSk7XG5cbiAgICAgIHJldHVybiB0eC53YWl0KCk7XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgLy8gUHJvdmlkZSBtb3JlIGRldGFpbGVkIGVycm9yIGluZm9ybWF0aW9uXG4gICAgICBjb25zb2xlLmVycm9yKCdQYXVzZS9VbnBhdXNlIGVycm9yIGRldGFpbHM6JywgZXJyb3IpO1xuXG4gICAgICBpZiAoZXJyb3IuY29kZSA9PT0gJ1VOS05PV05fRVJST1InICYmIGVycm9yLmVycm9yPy5jb2RlID09PSAtMzI2MDMpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdOZXR3b3JrIGVycm9yOiBQbGVhc2UgY2hlY2sgeW91ciBjb25uZWN0aW9uIGFuZCB0cnkgYWdhaW4uIFRoZSBBbW95IHRlc3RuZXQgbWF5IGJlIGV4cGVyaWVuY2luZyBpc3N1ZXMuJyk7XG4gICAgICB9IGVsc2UgaWYgKGVycm9yLmNvZGUgPT09ICdBQ1RJT05fUkVKRUNURUQnKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignVHJhbnNhY3Rpb24gd2FzIHJlamVjdGVkIGJ5IHVzZXInKTtcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IucmVhc29uKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgQ29udHJhY3QgZXJyb3I6ICR7ZXJyb3IucmVhc29ufWApO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gJHt0b2tlbkluZm8/LnBhdXNlZCA/ICd1bnBhdXNlJyA6ICdwYXVzZSd9IHRva2VuOiAke2Vycm9yLm1lc3NhZ2V9YCk7XG4gICAgICB9XG4gICAgfVxuICB9LCBbc2lnbmVyLCB0b2tlbkluZm9dKTtcblxuICAvLyBTY2hlZHVsZSB1cGdyYWRlXG4gIGNvbnN0IHNjaGVkdWxlVXBncmFkZSA9IHVzZUNhbGxiYWNrKGFzeW5jIChpbXBsZW1lbnRhdGlvbkFkZHJlc3M6IHN0cmluZywgZGVzY3JpcHRpb246IHN0cmluZykgPT4ge1xuICAgIGlmICghc2lnbmVyIHx8ICFVUEdSQURFX01BTkFHRVJfQUREUkVTUykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdXYWxsZXQgbm90IGNvbm5lY3RlZCcpO1xuICAgIH1cblxuICAgIGNvbnN0IGNvbnRyYWN0ID0gbmV3IGV0aGVycy5Db250cmFjdChVUEdSQURFX01BTkFHRVJfQUREUkVTUywgVXBncmFkZU1hbmFnZXJBQkksIHNpZ25lcik7XG4gICAgY29uc3QgU0VDVVJJVFlfVE9LRU5fQ09SRV9JRCA9IGV0aGVycy5rZWNjYWsyNTYoZXRoZXJzLnRvVXRmOEJ5dGVzKFwiU0VDVVJJVFlfVE9LRU5fQ09SRVwiKSk7XG4gICAgXG4gICAgY29uc3QgdHggPSBhd2FpdCBjb250cmFjdC5zY2hlZHVsZVVwZ3JhZGUoXG4gICAgICBTRUNVUklUWV9UT0tFTl9DT1JFX0lELFxuICAgICAgaW1wbGVtZW50YXRpb25BZGRyZXNzLFxuICAgICAgZGVzY3JpcHRpb25cbiAgICApO1xuICAgIFxuICAgIHJldHVybiB0eC53YWl0KCk7XG4gIH0sIFtzaWduZXJdKTtcblxuICAvLyBFeGVjdXRlIHVwZ3JhZGVcbiAgY29uc3QgZXhlY3V0ZVVwZ3JhZGUgPSB1c2VDYWxsYmFjayhhc3luYyAodXBncmFkZUlkOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIXNpZ25lciB8fCAhVVBHUkFERV9NQU5BR0VSX0FERFJFU1MpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignV2FsbGV0IG5vdCBjb25uZWN0ZWQnKTtcbiAgICB9XG5cbiAgICBjb25zdCBjb250cmFjdCA9IG5ldyBldGhlcnMuQ29udHJhY3QoVVBHUkFERV9NQU5BR0VSX0FERFJFU1MsIFVwZ3JhZGVNYW5hZ2VyQUJJLCBzaWduZXIpO1xuICAgIGNvbnN0IHR4ID0gYXdhaXQgY29udHJhY3QuZXhlY3V0ZVVwZ3JhZGUodXBncmFkZUlkKTtcbiAgICByZXR1cm4gdHgud2FpdCgpO1xuICB9LCBbc2lnbmVyXSk7XG5cbiAgLy8gRW1lcmdlbmN5IHVwZ3JhZGVcbiAgY29uc3QgZW1lcmdlbmN5VXBncmFkZSA9IHVzZUNhbGxiYWNrKGFzeW5jIChpbXBsZW1lbnRhdGlvbkFkZHJlc3M6IHN0cmluZywgZGVzY3JpcHRpb246IHN0cmluZykgPT4ge1xuICAgIGlmICghc2lnbmVyIHx8ICFVUEdSQURFX01BTkFHRVJfQUREUkVTUykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdXYWxsZXQgbm90IGNvbm5lY3RlZCcpO1xuICAgIH1cblxuICAgIGNvbnN0IGNvbnRyYWN0ID0gbmV3IGV0aGVycy5Db250cmFjdChVUEdSQURFX01BTkFHRVJfQUREUkVTUywgVXBncmFkZU1hbmFnZXJBQkksIHNpZ25lcik7XG4gICAgY29uc3QgU0VDVVJJVFlfVE9LRU5fQ09SRV9JRCA9IGV0aGVycy5rZWNjYWsyNTYoZXRoZXJzLnRvVXRmOEJ5dGVzKFwiU0VDVVJJVFlfVE9LRU5fQ09SRVwiKSk7XG4gICAgXG4gICAgY29uc3QgdHggPSBhd2FpdCBjb250cmFjdC5lbWVyZ2VuY3lVcGdyYWRlKFxuICAgICAgU0VDVVJJVFlfVE9LRU5fQ09SRV9JRCxcbiAgICAgIGltcGxlbWVudGF0aW9uQWRkcmVzcyxcbiAgICAgIGRlc2NyaXB0aW9uXG4gICAgKTtcbiAgICBcbiAgICByZXR1cm4gdHgud2FpdCgpO1xuICB9LCBbc2lnbmVyXSk7XG5cbiAgLy8gVG9nZ2xlIGVtZXJnZW5jeSBtb2RlXG4gIGNvbnN0IHRvZ2dsZUVtZXJnZW5jeU1vZGUgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFzaWduZXIgfHwgIVVQR1JBREVfTUFOQUdFUl9BRERSRVNTKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1dhbGxldCBub3QgY29ubmVjdGVkJyk7XG4gICAgfVxuXG4gICAgY29uc3QgY29udHJhY3QgPSBuZXcgZXRoZXJzLkNvbnRyYWN0KFVQR1JBREVfTUFOQUdFUl9BRERSRVNTLCBVcGdyYWRlTWFuYWdlckFCSSwgc2lnbmVyKTtcbiAgICBcbiAgICBjb25zdCB0eCA9IHVwZ3JhZGVJbmZvPy5lbWVyZ2VuY3lNb2RlQWN0aXZlIFxuICAgICAgPyBhd2FpdCBjb250cmFjdC5kZWFjdGl2YXRlRW1lcmdlbmN5TW9kZSgpXG4gICAgICA6IGF3YWl0IGNvbnRyYWN0LmFjdGl2YXRlRW1lcmdlbmN5TW9kZSgpO1xuICAgIFxuICAgIHJldHVybiB0eC53YWl0KCk7XG4gIH0sIFtzaWduZXIsIHVwZ3JhZGVJbmZvXSk7XG5cbiAgLy8gQ2FuY2VsIHVwZ3JhZGVcbiAgY29uc3QgY2FuY2VsVXBncmFkZSA9IHVzZUNhbGxiYWNrKGFzeW5jICh1cGdyYWRlSWQ6IHN0cmluZykgPT4ge1xuICAgIGlmICghc2lnbmVyIHx8ICFVUEdSQURFX01BTkFHRVJfQUREUkVTUykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdXYWxsZXQgbm90IGNvbm5lY3RlZCcpO1xuICAgIH1cblxuICAgIGNvbnN0IGNvbnRyYWN0ID0gbmV3IGV0aGVycy5Db250cmFjdChVUEdSQURFX01BTkFHRVJfQUREUkVTUywgVXBncmFkZU1hbmFnZXJBQkksIHNpZ25lcik7XG4gICAgY29uc3QgdHggPSBhd2FpdCBjb250cmFjdC5jYW5jZWxVcGdyYWRlKHVwZ3JhZGVJZCk7XG4gICAgcmV0dXJuIHR4LndhaXQoKTtcbiAgfSwgW3NpZ25lcl0pO1xuXG4gIC8vIFVwZGF0ZSB0b2tlbiBwcmljZVxuICBjb25zdCB1cGRhdGVUb2tlblByaWNlID0gdXNlQ2FsbGJhY2soYXN5bmMgKG5ld1ByaWNlOiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIXNpZ25lciB8fCAhU0VDVVJJVFlfVE9LRU5fQ09SRV9BRERSRVNTKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1dhbGxldCBub3QgY29ubmVjdGVkJyk7XG4gICAgfVxuXG4gICAgY29uc3QgY29udHJhY3QgPSBuZXcgZXRoZXJzLkNvbnRyYWN0KFNFQ1VSSVRZX1RPS0VOX0NPUkVfQUREUkVTUywgU2VjdXJpdHlUb2tlbkNvcmVBQkksIHNpZ25lcik7XG4gICAgY29uc3QgdHggPSBhd2FpdCBjb250cmFjdC51cGRhdGVUb2tlblByaWNlKG5ld1ByaWNlKTtcbiAgICByZXR1cm4gdHgud2FpdCgpO1xuICB9LCBbc2lnbmVyXSk7XG5cbiAgLy8gVXBkYXRlIGJvbnVzIHRpZXJzXG4gIGNvbnN0IHVwZGF0ZUJvbnVzVGllcnMgPSB1c2VDYWxsYmFjayhhc3luYyAobmV3Qm9udXNUaWVyczogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFzaWduZXIgfHwgIVNFQ1VSSVRZX1RPS0VOX0NPUkVfQUREUkVTUykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdXYWxsZXQgbm90IGNvbm5lY3RlZCcpO1xuICAgIH1cblxuICAgIGNvbnN0IGNvbnRyYWN0ID0gbmV3IGV0aGVycy5Db250cmFjdChTRUNVUklUWV9UT0tFTl9DT1JFX0FERFJFU1MsIFNlY3VyaXR5VG9rZW5Db3JlQUJJLCBzaWduZXIpO1xuICAgIGNvbnN0IHR4ID0gYXdhaXQgY29udHJhY3QudXBkYXRlQm9udXNUaWVycyhuZXdCb251c1RpZXJzKTtcbiAgICByZXR1cm4gdHgud2FpdCgpO1xuICB9LCBbc2lnbmVyXSk7XG5cbiAgLy8gVXBkYXRlIG1heCBzdXBwbHlcbiAgY29uc3QgdXBkYXRlTWF4U3VwcGx5ID0gdXNlQ2FsbGJhY2soYXN5bmMgKG5ld01heFN1cHBseTogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFzaWduZXIgfHwgIVNFQ1VSSVRZX1RPS0VOX0NPUkVfQUREUkVTUykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdXYWxsZXQgbm90IGNvbm5lY3RlZCcpO1xuICAgIH1cblxuICAgIGNvbnN0IGNvbnRyYWN0ID0gbmV3IGV0aGVycy5Db250cmFjdChTRUNVUklUWV9UT0tFTl9DT1JFX0FERFJFU1MsIFNlY3VyaXR5VG9rZW5Db3JlQUJJLCBzaWduZXIpO1xuICAgIGNvbnN0IG1heFN1cHBseVdlaSA9IGV0aGVycy5wYXJzZVVuaXRzKG5ld01heFN1cHBseSwgdG9rZW5JbmZvPy5kZWNpbWFscyB8fCAwKTtcbiAgICBjb25zdCB0eCA9IGF3YWl0IGNvbnRyYWN0LnVwZGF0ZU1heFN1cHBseShtYXhTdXBwbHlXZWkpO1xuICAgIHJldHVybiB0eC53YWl0KCk7XG4gIH0sIFtzaWduZXIsIHRva2VuSW5mb10pO1xuXG4gIC8vIEFkZCB0byB3aGl0ZWxpc3RcbiAgY29uc3QgYWRkVG9XaGl0ZWxpc3QgPSB1c2VDYWxsYmFjayhhc3luYyAoYWRkcmVzczogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFzaWduZXIgfHwgIVNFQ1VSSVRZX1RPS0VOX0NPUkVfQUREUkVTUykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdXYWxsZXQgbm90IGNvbm5lY3RlZCcpO1xuICAgIH1cblxuICAgIGNvbnN0IGNvbnRyYWN0ID0gbmV3IGV0aGVycy5Db250cmFjdChTRUNVUklUWV9UT0tFTl9DT1JFX0FERFJFU1MsIFNlY3VyaXR5VG9rZW5Db3JlQUJJLCBzaWduZXIpO1xuICAgIGNvbnN0IHR4ID0gYXdhaXQgY29udHJhY3QuYWRkVG9XaGl0ZWxpc3QoYWRkcmVzcyk7XG4gICAgcmV0dXJuIHR4LndhaXQoKTtcbiAgfSwgW3NpZ25lcl0pO1xuXG4gIC8vIFJlbW92ZSBmcm9tIHdoaXRlbGlzdFxuICBjb25zdCByZW1vdmVGcm9tV2hpdGVsaXN0ID0gdXNlQ2FsbGJhY2soYXN5bmMgKGFkZHJlc3M6IHN0cmluZykgPT4ge1xuICAgIGlmICghc2lnbmVyIHx8ICFTRUNVUklUWV9UT0tFTl9DT1JFX0FERFJFU1MpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignV2FsbGV0IG5vdCBjb25uZWN0ZWQnKTtcbiAgICB9XG5cbiAgICBjb25zdCBjb250cmFjdCA9IG5ldyBldGhlcnMuQ29udHJhY3QoU0VDVVJJVFlfVE9LRU5fQ09SRV9BRERSRVNTLCBTZWN1cml0eVRva2VuQ29yZUFCSSwgc2lnbmVyKTtcbiAgICBjb25zdCB0eCA9IGF3YWl0IGNvbnRyYWN0LnJlbW92ZUZyb21XaGl0ZWxpc3QoYWRkcmVzcyk7XG4gICAgcmV0dXJuIHR4LndhaXQoKTtcbiAgfSwgW3NpZ25lcl0pO1xuXG4gIC8vIFJlZnJlc2ggYWxsIGRhdGFcbiAgY29uc3QgcmVmcmVzaERhdGEgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgbG9hZFRva2VuSW5mbygpLFxuICAgICAgbG9hZFVwZ3JhZGVJbmZvKClcbiAgICBdKTtcbiAgfSwgW2xvYWRUb2tlbkluZm8sIGxvYWRVcGdyYWRlSW5mb10pO1xuXG4gIC8vIEluaXRpYWxpemUgb24gbW91bnRcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpbml0aWFsaXplUHJvdmlkZXIoKTtcbiAgfSwgW2luaXRpYWxpemVQcm92aWRlcl0pO1xuXG4gIC8vIExvYWQgZGF0YSB3aGVuIHByb3ZpZGVyIGlzIHJlYWR5XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKHByb3ZpZGVyICYmIHNpZ25lcikge1xuICAgICAgcmVmcmVzaERhdGEoKTtcbiAgICB9XG4gIH0sIFtwcm92aWRlciwgc2lnbmVyLCByZWZyZXNoRGF0YV0pO1xuXG4gIHJldHVybiB7XG4gICAgLy8gU3RhdGVcbiAgICBwcm92aWRlcixcbiAgICBzaWduZXIsXG4gICAgdG9rZW5JbmZvLFxuICAgIHVwZ3JhZGVJbmZvLFxuICAgIHBlbmRpbmdVcGdyYWRlcyxcbiAgICB1cGdyYWRlSGlzdG9yeSxcbiAgICBsb2FkaW5nLFxuICAgIGVycm9yLFxuICAgIFxuICAgIC8vIEFjdGlvbnNcbiAgICBpbml0aWFsaXplUHJvdmlkZXIsXG4gICAgbG9hZFRva2VuSW5mbyxcbiAgICBsb2FkVXBncmFkZUluZm8sXG4gICAgbWludFRva2VucyxcbiAgICB0b2dnbGVQYXVzZSxcbiAgICBzY2hlZHVsZVVwZ3JhZGUsXG4gICAgZXhlY3V0ZVVwZ3JhZGUsXG4gICAgZW1lcmdlbmN5VXBncmFkZSxcbiAgICB0b2dnbGVFbWVyZ2VuY3lNb2RlLFxuICAgIGNhbmNlbFVwZ3JhZGUsXG4gICAgcmVmcmVzaERhdGEsXG5cbiAgICAvLyBBZG1pbiBGdW5jdGlvbnNcbiAgICB1cGRhdGVUb2tlblByaWNlLFxuICAgIHVwZGF0ZUJvbnVzVGllcnMsXG4gICAgdXBkYXRlTWF4U3VwcGx5LFxuICAgIGFkZFRvV2hpdGVsaXN0LFxuICAgIHJlbW92ZUZyb21XaGl0ZWxpc3QsXG5cbiAgICAvLyBVdGlsaXRpZXNcbiAgICBzZXRFcnJvcixcbiAgICBzZXRMb2FkaW5nXG4gIH07XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VDYWxsYmFjayIsImV0aGVycyIsIlNlY3VyaXR5VG9rZW5Db3JlQUJJIiwiVXBncmFkZU1hbmFnZXJBQkkiLCJTRUNVUklUWV9UT0tFTl9DT1JFX0FERFJFU1MiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQU1PWV9TRUNVUklUWV9UT0tFTl9DT1JFX0FERFJFU1MiLCJVUEdSQURFX01BTkFHRVJfQUREUkVTUyIsIk5FWFRfUFVCTElDX0FNT1lfVVBHUkFERV9NQU5BR0VSX0FERFJFU1MiLCJ1c2VNb2R1bGFyVG9rZW4iLCJwcm92aWRlciIsInNldFByb3ZpZGVyIiwic2lnbmVyIiwic2V0U2lnbmVyIiwidG9rZW5JbmZvIiwic2V0VG9rZW5JbmZvIiwidXBncmFkZUluZm8iLCJzZXRVcGdyYWRlSW5mbyIsInBlbmRpbmdVcGdyYWRlcyIsInNldFBlbmRpbmdVcGdyYWRlcyIsInVwZ3JhZGVIaXN0b3J5Iiwic2V0VXBncmFkZUhpc3RvcnkiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImVycm9yIiwic2V0RXJyb3IiLCJpbml0aWFsaXplUHJvdmlkZXIiLCJ3aW5kb3ciLCJldGhlcmV1bSIsIkJyb3dzZXJQcm92aWRlciIsInNlbmQiLCJnZXRTaWduZXIiLCJFcnJvciIsImNvbnNvbGUiLCJsb2FkVG9rZW5JbmZvIiwiY29udHJhY3QiLCJDb250cmFjdCIsIm5hbWUiLCJzeW1ib2wiLCJ2ZXJzaW9uIiwidG90YWxTdXBwbHkiLCJtYXhTdXBwbHkiLCJkZWNpbWFscyIsInBhdXNlZCIsIm1ldGFkYXRhIiwiUHJvbWlzZSIsImFsbCIsImdldFRva2VuTWV0YWRhdGEiLCJmb3JtYXRVbml0cyIsInRva2VuUHJpY2UiLCJib251c1RpZXJzIiwidG9rZW5EZXRhaWxzIiwidG9rZW5JbWFnZVVybCIsImxvYWRVcGdyYWRlSW5mbyIsImVtZXJnZW5jeU1vZGVBY3RpdmUiLCJyZWdpc3RlcmVkTW9kdWxlcyIsInVwZ3JhZGVEZWxheSIsImVtZXJnZW5jeU1vZGVEdXJhdGlvbiIsInBlbmRpbmdVcGdyYWRlSWRzIiwiaXNFbWVyZ2VuY3lNb2RlQWN0aXZlIiwiZ2V0UmVnaXN0ZXJlZE1vZHVsZXMiLCJVUEdSQURFX0RFTEFZIiwiRU1FUkdFTkNZX01PREVfRFVSQVRJT04iLCJnZXRQZW5kaW5nVXBncmFkZUlkcyIsIk51bWJlciIsInBlbmRpbmdVcGdyYWRlc0RhdGEiLCJtYXAiLCJpZCIsInVwZ3JhZGUiLCJ1cGdyYWRlSWQiLCJtb2R1bGVJZCIsInByb3h5IiwibmV3SW1wbGVtZW50YXRpb24iLCJleGVjdXRlVGltZSIsImV4ZWN1dGVkIiwiY2FuY2VsbGVkIiwiZGVzY3JpcHRpb24iLCJTRUNVUklUWV9UT0tFTl9DT1JFX0lEIiwia2VjY2FrMjU2IiwidG9VdGY4Qnl0ZXMiLCJoaXN0b3J5IiwiZ2V0VXBncmFkZUhpc3RvcnkiLCJyZWNvcmQiLCJvbGRJbXBsZW1lbnRhdGlvbiIsInRpbWVzdGFtcCIsImV4ZWN1dG9yIiwiaXNFbWVyZ2VuY3kiLCJtaW50VG9rZW5zIiwiYWRkcmVzcyIsImFtb3VudCIsImFtb3VudFdlaSIsInBhcnNlVW5pdHMiLCJ0eCIsIm1pbnQiLCJ3YWl0IiwidG9nZ2xlUGF1c2UiLCJnYXNMaW1pdCIsInVucGF1c2UiLCJwYXVzZSIsImNvZGUiLCJyZWFzb24iLCJtZXNzYWdlIiwic2NoZWR1bGVVcGdyYWRlIiwiaW1wbGVtZW50YXRpb25BZGRyZXNzIiwiZXhlY3V0ZVVwZ3JhZGUiLCJlbWVyZ2VuY3lVcGdyYWRlIiwidG9nZ2xlRW1lcmdlbmN5TW9kZSIsImRlYWN0aXZhdGVFbWVyZ2VuY3lNb2RlIiwiYWN0aXZhdGVFbWVyZ2VuY3lNb2RlIiwiY2FuY2VsVXBncmFkZSIsInVwZGF0ZVRva2VuUHJpY2UiLCJuZXdQcmljZSIsInVwZGF0ZUJvbnVzVGllcnMiLCJuZXdCb251c1RpZXJzIiwidXBkYXRlTWF4U3VwcGx5IiwibmV3TWF4U3VwcGx5IiwibWF4U3VwcGx5V2VpIiwiYWRkVG9XaGl0ZWxpc3QiLCJyZW1vdmVGcm9tV2hpdGVsaXN0IiwicmVmcmVzaERhdGEiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useModularToken.ts\n"));

/***/ })

});